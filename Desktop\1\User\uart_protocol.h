#ifndef __UART_PROTOCOL_H
#define __UART_PROTOCOL_H

#include "stm32f10x.h"
#include "rtc.h"
#include "usart.h"

// 最大闹钟数量
#define MAX_ALARMS        10

// 协议命令定义
#define CMD_GET_TIME        0x01  // 获取当前时间
#define CMD_SET_TIME        0x02  // 设置当前时间
#define CMD_GET_ALARM       0x03  // 获取闹钟信息
#define CMD_SET_ALARM       0x04  // 设置闹钟信息
#define CMD_HELP            0x05  // 获取命令帮助信息
#define CMD_LIST_ALARMS     0x06  // 列出所有闹钟
#define CMD_DELETE_ALARM    0x07  // 删除指定闹钟
#define CMD_ENABLE_ALARM    0x08  // 启用指定闹钟
#define CMD_DISABLE_ALARM   0x09  // 禁用指定闹钟
#define CMD_GET_STATUS      0x0A  // 获取系统状态

// 协议帧结构
typedef struct {
    uint8_t cmd;             // 命令码
    uint8_t data_len;        // 数据长度
    uint8_t data[32];        // 数据内容，最多32字节
} UartProtocolFrame;

// 函数声明
void UART_Protocol_Init(void);
void UART_Protocol_Process(void);
void UART_Send_Time(void);
void UART_Send_Alarm(uint8_t index);
void UART_Send_Help(void);
void UART_Send_AlarmList(void);
void UART_Send_Status(void);

// 协议解析相关函数
uint8_t UART_Parse_Command(char *cmd_str);
void UART_Parse_Time(char *time_str);
void UART_Parse_Alarm(char *alarm_str);
void UART_Delete_Alarm(uint8_t index);
void UART_Enable_Alarm(uint8_t index);
void UART_Disable_Alarm(uint8_t index);
#endif /* __UART_PROTOCOL_H */ 
