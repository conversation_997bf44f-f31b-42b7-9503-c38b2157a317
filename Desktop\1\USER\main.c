#include "stm32f10x.h"
#include "sys.h"
#include "usart.h"
#include "timer.h"
#include "delay.h"
#include "rtc.h"
#include "led.h"
#include "clock_display.h"
#include "key.h"
#include "uart_protocol.h"
#include <string.h>

int main(void)
{
    uint8_t key;
    
    // ϵͳ��ʼ��
    SystemInit();
    
    // ʱ�ӳ�ʼ��
   // SysTick_Init();
    
    // LED��ʼ��
    LED_Init();
    
    // ������ʼ��
    KEY_Init();
    
    // ���ڳ�ʼ��
    uart1_init(115200);
    
    // ʵʱʱ�ӳ�ʼ��
    RTC_Init();
    
    // ��ʱ����ʼ��
    TIM2_Init(999, 71);  // 1ms��ʱ (72MHz / (71+1) / (999+1) = 1000Hz)
    TIM3_Init(499, 7199); // 500ms��ʱ (72MHz / (7199+1) / (499+1) = 2Hz)
    TIM4_Init();  // ��ʼ��TIM4����delay_us��delay_ms����
    
    // ����ͨ��Э���ʼ��
    UART_Protocol_Init();
    
    // ��ʾ��ʼ��
    Clock_Init();
    
    // �ֶ����������б�
    memset(&g_AlarmManager, 0, sizeof(g_AlarmManager));
    
    // ����һ��00:00:30������
    Alarm alarm;
    alarm.hour = 0;
    alarm.minute = 0;
    alarm.second = 30;
    alarm.enabled = 1;
    alarm.days = 0x7F; // ÿ��
    strcpy(alarm.name, "Test Alarm 30s");
    Alarm_Add(&g_AlarmManager, &alarm);
    
    // ��ӡϵͳ��ʼ�������Ϣ
    printf("Clock system initialized\r\n");
    printf("Current time: %04d-%02d-%02d %02d:%02d:%02d\r\n", 
           g_DateTime.year, g_DateTime.month, g_DateTime.day,
           g_DateTime.hour, g_DateTime.minute, g_DateTime.second);
    
    // ��ʾ��ǰ������Ϣ
    printf("Alarm list (%d alarms):\r\n", g_AlarmManager.count);
    for(uint8_t i=0; i<g_AlarmManager.count; i++) {
        Alarm* a = &g_AlarmManager.alarms[i];
        printf("[%d] %02d:%02d:%02d %s Status:%s Repeat:0x%02X\r\n", 
               i, a->hour, a->minute, a->second, 
               a->name, a->enabled ? "ON" : "OFF", a->days);
    }
    
    while(1)
    {
        // ������������
        key = KEY_Scan(0);
        if(key != KEY_NONE) {
            printf("��������: %d\r\n", key);  // �������
            Clock_ProcessKey(key);
        }
        
        // ��ʾʱ��
        Clock_Display(&g_DateTime);
        
        // ��������ͨ��Э��
        UART_Protocol_Process();
        
        // ��ʱ10ms������CPUռ��
        delay_ms(10);
    }
}
