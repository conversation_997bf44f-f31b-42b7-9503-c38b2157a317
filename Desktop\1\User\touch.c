#include "touch.h"
#include "lcd.h"
#include "delay.h"
#include <stdio.h>

// 触摸屏校准参数
uint8_t CMD_RDX = 0XD0;
uint8_t CMD_RDY = 0X90;

// 触摸屏校准参数
static uint8_t  tp_flag = 0;             // 触摸屏标志
static uint16_t tp_x = 0, tp_y = 0;      // 当前坐标
static uint16_t tp_x_temp = 0, tp_y_temp = 0; // 临时坐标

// 触摸屏初始化
void TP_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    
    // 使能时钟
    RCC_APB2PeriphClockCmd(TP_CS_CLK | TP_DOUT_CLK | TP_DIN_CLK | TP_CLK_CLK | TP_IRQ_CLK, ENABLE);
    
    // CS引脚
    GPIO_InitStructure.GPIO_Pin = TP_CS_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;  // 推挽输出
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(TP_CS_PORT, &GPIO_InitStructure);
    
    // CLK引脚
    GPIO_InitStructure.GPIO_Pin = TP_CLK_PIN;
    GPIO_Init(TP_CLK_PORT, &GPIO_InitStructure);
    
    // DIN引脚
    GPIO_InitStructure.GPIO_Pin = TP_DIN_PIN;
    GPIO_Init(TP_DIN_PORT, &GPIO_InitStructure);
    
    // DOUT引脚
    GPIO_InitStructure.GPIO_Pin = TP_DOUT_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;  // 上拉输入
    GPIO_Init(TP_DOUT_PORT, &GPIO_InitStructure);
    
    // IRQ引脚
    GPIO_InitStructure.GPIO_Pin = TP_IRQ_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;  // 上拉输入
    GPIO_Init(TP_IRQ_PORT, &GPIO_InitStructure);
    
    // 初始化IO
    TP_CS_SET;
    TP_CLK_CLR;
    TP_DIN_CLR;
}

// 延时函数
static void TP_Delay(uint16_t count)
{
    uint16_t i;
    for(i = 0; i < count; i++);
}

// SPI写数据
static void TP_Write_Byte(uint8_t data)
{
    uint8_t i;
    for(i = 0; i < 8; i++)
    {
        if(data & 0x80)
            TP_DIN_SET;
        else
            TP_DIN_CLR;
        
        data <<= 1;
        TP_CLK_CLR;
        TP_Delay(1);
        TP_CLK_SET;
    }
}

// SPI读数据
static uint16_t TP_Read_AD(uint8_t cmd)
{
    uint8_t i;
    uint16_t data = 0;
    
    TP_CLK_CLR;
    TP_CS_CLR;
    
    TP_Write_Byte(cmd);
    
    // 等待转换完成
    TP_Delay(6);
    
    TP_CLK_CLR;
    TP_Delay(1);
    TP_CLK_SET;
    TP_Delay(1);
    TP_CLK_CLR;
    
    for(i = 0; i < 16; i++)
    {
        data <<= 1;
        TP_CLK_CLR;
        TP_Delay(1);
        TP_CLK_SET;
        if(TP_DOUT)
            data++;
    }
    
    data >>= 4;   // 只有12位有效
    TP_CS_SET;
    
    return data;
}

// 读取坐标
uint8_t TP_Read_XY(uint16_t *x, uint16_t *y)
{
    uint16_t xtemp, ytemp;
    
    xtemp = TP_Read_AD(CMD_RDX);
    ytemp = TP_Read_AD(CMD_RDY);
    
    *x = xtemp;
    *y = ytemp;
    
    return 1;
}

// 连续读取两次坐标，取平均值
uint8_t TP_Read_XY2(uint16_t *x, uint16_t *y)
{
    uint16_t x1, y1;
    uint16_t x2, y2;
    
    if(!TP_Read_XY(&x1, &y1))
        return 0;
    if(!TP_Read_XY(&x2, &y2))
        return 0;
    
    // 前后两次采样偏差不能超过50
    if(((x2 <= x1 && x1 < x2 + 50) || (x1 <= x2 && x2 < x1 + 50)) &&
       ((y2 <= y1 && y1 < y2 + 50) || (y1 <= y2 && y2 < y1 + 50)))
    {
        *x = (x1 + x2) / 2;
        *y = (y1 + y2) / 2;
        return 1;
    }
    
    return 0;
}

// 扫描触摸屏
uint8_t TP_Scan(uint8_t mode)
{
    if(!mode)
    {
        if(TP_IRQ == 0)  // 有触摸
        {
            if(TP_Read_XY2(&tp_x, &tp_y))
            {
                tp_x_temp = tp_x;
                tp_y_temp = tp_y;
                
                // 将ADC值转换为屏幕坐标
                // 这里使用简化的转换方式，实际应该根据校准参数进行转换
                tp_x = tp_x / 4096.0 * 240;
                tp_y = tp_y / 4096.0 * 320;
                
                // 检查坐标是否在屏幕范围内
                if(tp_x > 240) tp_x = 240;
                if(tp_y > 320) tp_y = 320;
                
                tp_flag = 1;  // 触摸有效
                return 1;
            }
        }
        else
        {
            tp_flag = 0;  // 触摸无效
        }
    }
    else  // 持续扫描模式
    {
        if(TP_IRQ == 0)  // 有触摸
        {
            if(TP_Read_XY2(&tp_x, &tp_y))
            {
                tp_x_temp = tp_x;
                tp_y_temp = tp_y;
                
                // 将ADC值转换为屏幕坐标
                tp_x = tp_x / 4096.0 * 240;
                tp_y = tp_y / 4096.0 * 320;
                
                // 检查坐标是否在屏幕范围内
                if(tp_x > 240) tp_x = 240;
                if(tp_y > 320) tp_y = 320;
                
                tp_flag = 1;  // 触摸有效
                return 1;
            }
        }
    }
    
    return 0;
}

// 获取X坐标
uint16_t TP_Get_X(void)
{
    return tp_x;
}

// 获取Y坐标
uint16_t TP_Get_Y(void)
{
    return tp_y;
}

// 校准触摸屏
void TP_Adjust(void)
{
    // 简化版本，实际应该进行校准
    printf("Touch screen calibration not implemented\r\n");
} 