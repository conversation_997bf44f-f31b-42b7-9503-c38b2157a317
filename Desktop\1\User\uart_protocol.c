#include "uart_protocol.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>

// Initialize UART protocol
void UART_Protocol_Init(void)
{
    // Print welcome message
    printf("\r\n====================================\r\n");
    printf("Clock System UART Protocol v1.0\r\n");
    printf("Type 'help' for command list\r\n");
    printf("====================================\r\n");
}

// Process received UART data
void UART_Protocol_Process(void)
{
    uint8_t cmd;
    char *cmd_str;
    
    // Check if data is available
    if(USART_RX_STA & 0x8000)
    {
        // Get received data length
        uint16_t len = USART_RX_STA & 0x3FFF;
        
        // Add string terminator
        USART_RX_BUF[len] = 0;
        
        // Convert to string
        cmd_str = (char *)USART_RX_BUF;
        
        // Parse command
        cmd = UART_Parse_Command(cmd_str);
        
        // Execute command
        switch(cmd)
        {
            case CMD_GET_TIME:
                UART_Send_Time();
                break;
                
            case CMD_SET_TIME:
                UART_Parse_Time(cmd_str);
                break;
                
            case CMD_GET_ALARM:
                // Parse alarm index
                if(len > 10) { // getAlarm command should be longer
                    uint8_t index = atoi((const char*)&cmd_str[9]);
                    if(index < g_AlarmManager.count) {
                        UART_Send_Alarm(index);
                    } else {
                        printf("ERROR: Invalid alarm index\r\n");
                    }
                } else {
                    printf("ERROR: Missing alarm index\r\n");
                }
                break;
                
            case CMD_SET_ALARM:
                UART_Parse_Alarm(cmd_str);
                break;
                
            case CMD_HELP:
                UART_Send_Help();
                break;
                
            default:
                printf("ERROR: Unknown command\r\n");
                printf("Type 'help' for command list\r\n");
                break;
        }
        
        // Clear receive flag
        USART_RX_STA = 0;
    }
}

// Parse command string
uint8_t UART_Parse_Command(char *cmd_str)
{
    if(strncmp(cmd_str, "getTime", 7) == 0) {
        return CMD_GET_TIME;
    } else if(strncmp(cmd_str, "setTime", 7) == 0) {
        return CMD_SET_TIME;
    } else if(strncmp(cmd_str, "getAlarm", 8) == 0) {
        return CMD_GET_ALARM;
    } else if(strncmp(cmd_str, "setAlarm", 8) == 0) {
        return CMD_SET_ALARM;
    } else if(strncmp(cmd_str, "help", 4) == 0) {
        return CMD_HELP;
    }
    
    return 0xFF; // Invalid command
}

// Send current time
void UART_Send_Time(void)
{
    printf("TIME=%04d-%02d-%02d,%02d:%02d:%02d,W%d\r\n", 
           g_DateTime.year, g_DateTime.month, g_DateTime.day,
           g_DateTime.hour, g_DateTime.minute, g_DateTime.second,
           g_DateTime.week);
}

// Parse and set time
void UART_Parse_Time(char *time_str)
{
    // Format: setTime YYYY-MM-DD,HH:MM:SS
    DateTime new_time;
    
    // Find data start position
    char *data_start = strchr(time_str, ' ');
    if(data_start == NULL) {
        printf("ERROR: Invalid time format\r\n");
        printf("Correct format: setTime YYYY-MM-DD,HH:MM:SS\r\n");
        return;
    }
    
    data_start++; // Skip space
    
    // Parse year-month-day
    if(sscanf(data_start, "%hu-%hhu-%hhu,%hhu:%hhu:%hhu", 
              &new_time.year, &new_time.month, &new_time.day,
              &new_time.hour, &new_time.minute, &new_time.second) != 6) {
        printf("ERROR: Invalid time format\r\n");
        printf("Correct format: setTime YYYY-MM-DD,HH:MM:SS\r\n");
        return;
    }
    
    // Validate time
    if(new_time.year < 2000 || new_time.year > 2099 ||
       new_time.month < 1 || new_time.month > 12 ||
       new_time.day < 1 || new_time.day > 31 ||
       new_time.hour > 23 || new_time.minute > 59 || new_time.second > 59) {
        printf("ERROR: Invalid time parameters\r\n");
        return;
    }
    
    // Calculate weekday
    new_time.week = GetDayOfWeek(new_time.year, new_time.month, new_time.day);
    new_time.millisecond = 0;
    
    // Set new time
    RTC_SetTime(&new_time);
    
    printf("SUCCESS: Time set to %04d-%02d-%02d %02d:%02d:%02d Weekday:%d\r\n",
           new_time.year, new_time.month, new_time.day,
           new_time.hour, new_time.minute, new_time.second,
           new_time.week);
}

// Send alarm information
void UART_Send_Alarm(uint8_t index)
{
    if(index >= g_AlarmManager.count) {
        printf("ERROR: Invalid alarm index\r\n");
        return;
    }
    
    Alarm *alarm = &g_AlarmManager.alarms[index];
    
    // Generate repeat string
    char days_str[32] = {0};
    if(alarm->days == 0x7F) {
        strcpy(days_str, "Everyday");
    } else if(alarm->days == 0x1F) {
        strcpy(days_str, "Weekdays");
    } else if(alarm->days == 0x60) {
        strcpy(days_str, "Weekend");
    } else {
        if(alarm->days & 0x01) strcat(days_str, "Sun,");
        if(alarm->days & 0x02) strcat(days_str, "Mon,");
        if(alarm->days & 0x04) strcat(days_str, "Tue,");
        if(alarm->days & 0x08) strcat(days_str, "Wed,");
        if(alarm->days & 0x10) strcat(days_str, "Thu,");
        if(alarm->days & 0x20) strcat(days_str, "Fri,");
        if(alarm->days & 0x40) strcat(days_str, "Sat,");
        // Remove last comma
        if(strlen(days_str) > 0) {
            days_str[strlen(days_str) - 1] = 0;
        }
    }
    
    printf("ALARM%d=%02d:%02d:%02d,%s,%s,%d\r\n", 
           index, alarm->hour, alarm->minute, alarm->second,
           days_str, alarm->name, alarm->enabled);
}

// Parse and set alarm
void UART_Parse_Alarm(char *alarm_str)
{
    // Format: setAlarm INDEX,HH:MM:SS,DAYS,NAME,ENABLED
    // Example: setAlarm 0,08:00:00,1111100,Wake Up,1
    
    Alarm new_alarm;
    uint8_t index;
    char days_str[20];
    char name[20];
    uint8_t enabled;
    
    // Find data start position
    char *data_start = strchr(alarm_str, ' ');
    if(data_start == NULL) {
        printf("ERROR: Invalid alarm format\r\n");
        printf("Correct format: setAlarm INDEX,HH:MM:SS,DAYS,NAME,ENABLED\r\n");
        return;
    }
    
    data_start++; // Skip space
    
    // Parse data
    int fields = sscanf(data_start, "%hhu,%hhu:%hhu:%hhu,%[^,],%[^,],%hhu", 
                        &index, &new_alarm.hour, &new_alarm.minute, &new_alarm.second,
                        days_str, name, &enabled);
    
    if(fields != 7) {
        printf("ERROR: Invalid alarm format\r\n");
        printf("Correct format: setAlarm INDEX,HH:MM:SS,DAYS,NAME,ENABLED\r\n");
        return;
    }
    
    // Validate parameters
    if(new_alarm.hour > 23 || new_alarm.minute > 59 || new_alarm.second > 59) {
        printf("ERROR: Invalid alarm time parameters\r\n");
        return;
    }
    
    // Set alarm name
    strncpy(new_alarm.name, name, sizeof(new_alarm.name) - 1);
    new_alarm.name[sizeof(new_alarm.name) - 1] = 0; // Ensure string ends
    
    // Set alarm state
    new_alarm.enabled = enabled ? 1 : 0;
    
    // Parse repeat days
    new_alarm.days = 0;
    for(int i = 0; i < strlen(days_str) && i < 7; i++) {
        if(days_str[i] == '1') {
            new_alarm.days |= (1 << i);
        }
    }
    
    // Set or add alarm
    if(index < g_AlarmManager.count) {
        // Update existing alarm
        g_AlarmManager.alarms[index] = new_alarm;
        printf("SUCCESS: Alarm %d updated\r\n", index);
    } else if(index == g_AlarmManager.count && g_AlarmManager.count < MAX_ALARMS) {
        // Add new alarm
        g_AlarmManager.alarms[g_AlarmManager.count] = new_alarm;
        g_AlarmManager.count++;
        printf("SUCCESS: New alarm added, index=%d\r\n", g_AlarmManager.count - 1);
    } else {
        printf("ERROR: Invalid alarm index\r\n");
    }
}

// Send help information
void UART_Send_Help(void)
{
    printf("\r\n========== Clock System UART Command Help ==========\r\n");
    printf("Available commands:\r\n\r\n");
    
    printf("1. getTime\r\n");
    printf("   - Description: Get current system time\r\n");
    printf("   - Format: getTime\r\n");
    printf("   - Example: getTime\r\n\r\n");
    
    printf("2. setTime YYYY-MM-DD,HH:MM:SS\r\n");
    printf("   - Description: Set system time\r\n");
    printf("   - Format: setTime YYYY-MM-DD,HH:MM:SS\r\n");
    printf("   - Parameters:\r\n");
    printf("     YYYY: Year (2000-2099)\r\n");
    printf("     MM: Month (01-12)\r\n");
    printf("     DD: Day (01-31)\r\n");
    printf("     HH: Hour (00-23)\r\n");
    printf("     MM: Minute (00-59)\r\n");
    printf("     SS: Second (00-59)\r\n");
    printf("   - Example: setTime 2023-12-31,23:59:50\r\n\r\n");
    
    printf("3. getAlarm INDEX\r\n");
    printf("   - Description: Get alarm by index\r\n");
    printf("   - Format: getAlarm INDEX\r\n");
    printf("   - Parameters:\r\n");
    printf("     INDEX: Alarm index (0-%d)\r\n", MAX_ALARMS - 1);
    printf("   - Example: getAlarm 0\r\n\r\n");
    
    printf("4. setAlarm INDEX,HH:MM:SS,DAYS,NAME,ENABLED\r\n");
    printf("   - Description: Set or create alarm\r\n");
    printf("   - Format: setAlarm INDEX,HH:MM:SS,DAYS,NAME,ENABLED\r\n");
    printf("   - Parameters:\r\n");
    printf("     INDEX: Alarm index (0-%d)\r\n", MAX_ALARMS - 1);
    printf("     HH:MM:SS: Alarm time\r\n");
    printf("     DAYS: Repeat days pattern, 7-bit binary (Sun-Sat)\r\n");
    printf("        Example patterns:\r\n");
    printf("        1111111 - Every day\r\n");
    printf("        0111110 - Weekdays only\r\n");
    printf("        1000001 - Weekends only\r\n");
    printf("     NAME: Alarm name (no commas)\r\n");
    printf("     ENABLED: 1=enabled, 0=disabled\r\n");
    printf("   - Examples:\r\n");
    printf("     setAlarm 0,07:00:00,0111110,Wake Up,1\r\n");
    printf("     setAlarm 1,22:30:00,1111111,Bedtime,1\r\n\r\n");
    
    printf("5. help\r\n");
    printf("   - Description: Show this help information\r\n");
    printf("   - Format: help\r\n\r\n");
    
    printf("Note: All commands must end with Enter key (CR+LF).\r\n");
    printf("===============================================\r\n");
} 