#ifndef __KEY_H
#define __KEY_H

#include "stm32f10x.h"
#include "sys.h"

// 按键定义
#define KEY1_PIN     GPIO_Pin_15  // PA15 - 页面切换（上拉输入）
#define KEY1_PORT    GPIOA
#define KEY1_CLK     RCC_APB2Periph_GPIOA

#define KEY2_PIN     GPIO_Pin_5   // PC5 - 确定（上拉输入）
#define KEY2_PORT    GPIOC
#define KEY2_CLK     RCC_APB2Periph_GPIOC

#define KEY3_PIN     GPIO_Pin_0   // PA0 - 选择（下拉输入）
#define KEY3_PORT    GPIOA
#define KEY3_CLK     RCC_APB2Periph_GPIOA

// 按键状态定义（针对KEY1和KEY2，上拉输入）
#define KEY1_PRESSED  0  // 按下为低电平(上拉输入)
#define KEY1_RELEASED 1  // 松开为高电平

// 按键状态定义（针对KEY3，下拉输入）
#define KEY3_PRESSED  1  // 按下为高电平(下拉输入)
#define KEY3_RELEASED 0  // 松开为低电平

// 按键值定义
#define KEY_NONE     0
#define KEY1_PRESSED 1  // 页面切换
#define KEY2_PRESSED 2  // 确定
#define KEY3_PRESSED 3  // 选择

// 函数声明
void KEY_Init(void);
uint8_t KEY_Scan(uint8_t mode);

#endif /* __KEY_H */ 