#include "clock_display.h"
#include "key.h"
#include <math.h>
#include <stdio.h>
#include <string.h>

// 数学常量
#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

// 星期和月份名称数组
static const char* weekday_names[] = {"Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"};
static const char* month_names[] = {"", "January", "February", "March", "April", "May", "June",
                                   "July", "August", "September", "October", "November", "December"};

// 上一次绘制的时间，用于清除旧指针
static DateTime g_LastDrawTime = {0};

// 自定义LCD_DrawPoint_big函数实现
void LCD_DrawPoint_big(u16 x,u16 y)
{
    LCD_Fill(x-1, y-1, x+1, y+1, POINT_COLOR);
}

// 全局变量
static uint8_t g_CurrentPage = PAGE_CLOCK;      // 当前页面
static uint8_t g_CurrentAlarmIndex = 0;         // 当前编辑的闹钟索引
static uint8_t g_NeedRefresh = 1;               // 是否需要刷新显示
static uint8_t g_LastSecond = 0;                // 上次更新的秒数
static uint8_t g_LastMinute = 0;                // 上次更新的分钟
static uint8_t g_LastHour = 0;                  // 上次更新的小时
static uint8_t g_LastDay = 0;                   // 上次更新的日期
static uint8_t g_LastMonth = 0;                 // 上次更新的月份
static uint16_t g_LastYear = 0;                 // 上次更新的年份
static uint8_t g_LastWeek = 0;                  // 上次更新的星期
static uint8_t g_LastUpdateTime = 0;            // 上次更新的时间（秒）
static uint8_t g_SelectedItem = 0;              // 当前选中的项目
static uint8_t g_EditingField = 0;              // 当前编辑的字段（闹钟编辑时）
static uint8_t g_TimeEditMode = 0;              // 时间编辑模式：0-无，1-小时，2-分钟，3-秒
static Alarm g_TempAlarm;                       // 临时闹钟存储
static uint8_t g_FirstTimeEnter = 1;            // 标记是否是首次进入编辑页面
static DateTime g_TempDateTime;                 // 临时日期时间存储

// 选择项回调函数声明
static void SelectCallback_GotoAlarmList(void);
static void SelectCallback_GotoClock(void);
static void SelectCallback_AddAlarm(void);
static void SelectCallback_EditAlarm(uint8_t index);
static void SelectCallback_DeleteAlarm(uint8_t index);
static void SelectCallback_SaveAlarm(void);
static void SelectCallback_CancelEdit(void);
static void SelectCallback_SetTime(void);
static void SelectCallback_SaveTime(void);
static void SelectCallback_CancelTimeSet(void);

// 时间刷新函数声明
void Clock_RefreshTimeDigit(DateTime* time);

// 初始化时钟显示
void Clock_Init(void)
{
    // 初始化LCD
    LCD_Init();
    LCD_Clear(WHITE);
    
    // 设置默认页面
    g_CurrentPage = PAGE_CLOCK;
    g_NeedRefresh = 1;
}

// 切换页面
void Clock_ChangePage(uint8_t page)
{
    // 清空屏幕
    LCD_Clear(WHITE);
    
    // 设置页面
    g_CurrentPage = page;
    
    // 重置选中项
    g_SelectedItem = 0;
    
    // 如果从编辑页面切换出去，确保清除编辑状态
    if (page != PAGE_ALARM_EDIT) {
        g_EditingField = 0;
        g_TimeEditMode = 0;
    }
    
    // 设置刷新标志
    g_NeedRefresh = 1;
}

// 显示数字时钟页面
void Clock_DrawDigitalTime(DateTime* time)
{
    char buffer[30];
    static const char* weekdays[] = {"Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"};
    
    // 检查是否需要全屏刷新
    if (g_NeedRefresh) {
        // 清除整个显示区域，使用黑色背景
        LCD_Clear(BLACK);
        
        // 显示标题
        POINT_COLOR = RED;
        BACK_COLOR = BLACK;
        LCD_ShowString(85, 10, 200, 24, 24, (u8*)"CLOCK");
        
        // 显示初始时间和分隔符，使用更宽的间距
        // 小时: 60-84   冒号1: 84-96   分钟: 96-120   冒号2: 120-132   秒: 132-156
        LCD_ShowString(60, 110, 40, 24, 24, (u8*)"00");
        LCD_ShowString(84, 110, 12, 24, 24, (u8*)":");
        LCD_ShowString(96, 110, 40, 24, 24, (u8*)"00");
        LCD_ShowString(120, 110, 12, 24, 24, (u8*)":");
        LCD_ShowString(132, 110, 40, 24, 24, (u8*)"00");
        
        // 绘制底部菜单栏
        
        // 闹钟列表按钮
        if (g_SelectedItem == 0) {
            POINT_COLOR = RED;
            LCD_Fill(20, 200, 110, 230, GRAY); // 高亮背景为灰色
            BACK_COLOR = GRAY;
        } else {
            POINT_COLOR = GRAY;
            LCD_Fill(20, 200, 110, 230, BLACK); // 普通背景为黑色
            BACK_COLOR = BLACK;
        }
        LCD_DrawRectangle(20, 200, 110, 230);
        LCD_ShowString(30, 208, 200, 16, 16, (u8*)"ALARM LIST");
        
        // 时间设置按钮
        if (g_SelectedItem == 1) {
    POINT_COLOR = RED;
            LCD_Fill(130, 200, 220, 230, GRAY); // 高亮背景为灰色
            BACK_COLOR = GRAY;
        } else {
            POINT_COLOR = GRAY;
            LCD_Fill(130, 200, 220, 230, BLACK); // 普通背景为黑色
            BACK_COLOR = BLACK;
        }
        LCD_DrawRectangle(130, 200, 220, 230);
        LCD_ShowString(140, 208, 200, 16, 16, (u8*)"SET TIME");
        
        // 恢复默认背景色
        BACK_COLOR = BLACK;
        
        // 强制全部重绘
        g_LastSecond = 0xFF;
        g_LastMinute = 0xFF;
        g_LastHour = 0xFF;
        g_LastDay = 0xFF;
        g_LastMonth = 0xFF;
        g_LastYear = 0xFFFF;
        g_LastWeek = 0xFF;
        
        g_NeedRefresh = 0;
    }
    
    // 只更新变化的部分
    if (g_LastUpdateTime != time->second || g_LastHour != time->hour || 
        g_LastMinute != time->minute || g_LastSecond != time->second ||
        g_LastDay != time->day || g_LastMonth != time->month || 
        g_LastYear != time->year || g_LastWeek != time->week) {
        
        Clock_RefreshTimeDigit(time);
        g_LastUpdateTime = time->second;
    }
}

// 绘制时钟表盘
void Clock_DrawDial(void)
{
    int i;
    int x, y;
    float angle;
    
    // 绘制表盘外圈
    POINT_COLOR = BLACK;
    LCD_Draw_Circle(CLOCK_CENTER_X, CLOCK_CENTER_Y, CLOCK_RADIUS);
    
    // 绘制刻度
    for(i = 0; i < 12; i++) {
        angle = i * 30 * 3.14159 / 180;
        x = CLOCK_CENTER_X + (CLOCK_RADIUS - 10) * sin(angle);
        y = CLOCK_CENTER_Y - (CLOCK_RADIUS - 10) * cos(angle);
        
        // 绘制小时刻度
        LCD_DrawPoint_big(x, y);
    }
}



// 显示日期
void Clock_DrawDate(DateTime* time)
{
    // 此函数不再单独使用，功能已合并到Clock_DrawDigitalTime中
    // 保留函数以维持兼容性
    Clock_DrawDigitalTime(time);
}

// 绘制闹钟列表页面
void Clock_DrawAlarmList(void)
{
    uint8_t i;
    char buffer[30];
    static const char* weekdays[] = {"Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"};
    
    // 清屏，使用黑色背景
    LCD_Clear(BLACK);
    BACK_COLOR = BLACK;
    
    // 显示标题
    POINT_COLOR = RED;
    LCD_ShowString(10, 10, 200, 24, 24, (u8*)"Alarm List");
    
    // 显示闹钟列表
    for(i = 0; i < g_AlarmManager.count && i < 5; i++) {
        Alarm* alarm = &g_AlarmManager.alarms[i];
        
        // 绘制闹钟项背景
        if (g_SelectedItem == i + 1) {
            // 选中项高亮显示
            POINT_COLOR = RED;
            LCD_Fill(10, 50 + i * 40, 230, 80 + i * 40, GRAY);
            BACK_COLOR = GRAY;
        } else if(alarm->enabled) {
            POINT_COLOR = RED;
            LCD_DrawRectangle(10, 50 + i * 40, 230, 80 + i * 40);
            BACK_COLOR = BLACK;
        } else {
            POINT_COLOR = GRAY;
            LCD_DrawRectangle(10, 50 + i * 40, 230, 80 + i * 40);
            BACK_COLOR = BLACK;
        }
        
        // 显示闹钟时间
        sprintf(buffer, "%02d:%02d:%02d", alarm->hour, alarm->minute, alarm->second);
        LCD_ShowString(20, 55 + i * 40, 200, 16, 16, (u8*)buffer);
        
        // 显示闹钟名称
        LCD_ShowString(120, 55 + i * 40, 200, 16, 16, (u8*)alarm->name);
        
        // 显示重复日期
        POINT_COLOR = (g_SelectedItem == i + 1) ? RED : GRAY;
        buffer[0] = 0;
        for(uint8_t j = 0; j < 7; j++) {
            if(alarm->days & (1 << j)) {
                strcat(buffer, weekdays[j]);
                strcat(buffer, " ");
            }
        }
        LCD_ShowString(20, 75 + i * 40, 200, 16, 12, (u8*)buffer);
    }
    
    // 显示添加按钮
    if (g_SelectedItem == g_AlarmManager.count + 1) {
        // 选中项高亮显示
        POINT_COLOR = RED;
        LCD_Fill(10, 50 + i * 40, 230, 80 + i * 40, GRAY);
        BACK_COLOR = GRAY;
    } else {
    POINT_COLOR = RED;
        BACK_COLOR = BLACK;
    }
    LCD_DrawRectangle(10, 50 + i * 40, 230, 80 + i * 40);
    LCD_ShowString(100, 55 + i * 40, 200, 16, 16, (u8*)"+ Add");
    
    // 绘制返回按钮
    if (g_SelectedItem == 0) {
        // 选中项高亮显示
        POINT_COLOR = RED;
        LCD_Fill(10, 200, 230, 230, GRAY);
        BACK_COLOR = GRAY;
    } else {
        POINT_COLOR = GRAY;
        BACK_COLOR = BLACK;
    }
    LCD_DrawRectangle(10, 200, 230, 230);
    LCD_ShowString(90, 208, 200, 16, 16, (u8*)"BACK");
    
    // 恢复默认背景色
    BACK_COLOR = BLACK;
    g_NeedRefresh = 0;
}

// 绘制闹钟编辑页面
void Clock_DrawAlarmEdit(uint8_t index)
{
    char buffer[30];
    
    // 清屏，使用黑色背景
    LCD_Clear(BLACK);
    BACK_COLOR = BLACK;
    
    // 显示标题
    POINT_COLOR = RED;
    
    if(index < g_AlarmManager.count) {
        LCD_ShowString(10, 10, 200, 24, 24, (u8*)"Edit Alarm");
        
        if(g_FirstTimeEnter) {
            // 复制到临时闹钟
            memcpy(&g_TempAlarm, &g_AlarmManager.alarms[index], sizeof(Alarm));
            printf("Loading existing alarm: %02d:%02d:%02d, Days=%02X\r\n", 
                g_TempAlarm.hour, g_TempAlarm.minute, g_TempAlarm.second, g_TempAlarm.days);
            g_FirstTimeEnter = 0;
        }
    } else {
        LCD_ShowString(10, 10, 200, 24, 24, (u8*)"New Alarm");
        
        if(g_FirstTimeEnter) {
            // 初始化新闹钟
            memset(&g_TempAlarm, 0, sizeof(Alarm));
            sprintf(g_TempAlarm.name, "Alarm %d", g_AlarmManager.count);
            g_TempAlarm.enabled = 1;
            g_TempAlarm.days = 0x7F; // 每天
            printf("Created new alarm template\r\n");
            g_FirstTimeEnter = 0;
        }
    }
    
    // 调试打印当前闹钟信息
    printf("Current alarm editing: Time=%02d:%02d:%02d, Status=%d\r\n", 
        g_TempAlarm.hour, g_TempAlarm.minute, g_TempAlarm.second, g_TempAlarm.enabled);
    
    // 显示时间设置 (g_EditingField == 1)
    POINT_COLOR = GRAY;
    LCD_ShowString(20, 50, 200, 16, 16, (u8*)"Time:");
    
    if (g_SelectedItem == 1) {
        if (g_EditingField == 1) {
            // 正在编辑时间
            if (g_TimeEditMode == 1) {
                // 编辑小时，高亮显示小时部分
                POINT_COLOR = RED;
                BACK_COLOR = GRAY;
                sprintf(buffer, "%02d", g_TempAlarm.hour);
                LCD_Fill(100, 50, 115, 66, GRAY);
                LCD_ShowString(100, 50, 15, 16, 16, (u8*)buffer);
                
                // 显示分钟和秒（不高亮）
                POINT_COLOR = GRAY;
                BACK_COLOR = BLACK;
                sprintf(buffer, ":%02d:%02d", g_TempAlarm.minute, g_TempAlarm.second);
                LCD_ShowString(115, 50, 200, 16, 16, (u8*)buffer);
            } else if (g_TimeEditMode == 2) {
                // 编辑分钟，高亮显示分钟部分
                POINT_COLOR = GRAY;
                BACK_COLOR = BLACK;
                sprintf(buffer, "%02d:", g_TempAlarm.hour);
                LCD_ShowString(100, 50, 200, 16, 16, (u8*)buffer);
                
                // 高亮显示分钟
                POINT_COLOR = RED;
                BACK_COLOR = GRAY;
                sprintf(buffer, "%02d", g_TempAlarm.minute);
                LCD_Fill(118, 50, 133, 66, GRAY);
                LCD_ShowString(118, 50, 15, 16, 16, (u8*)buffer);
                
                // 显示秒钟（不高亮）
                POINT_COLOR = GRAY;
                BACK_COLOR = BLACK;
                sprintf(buffer, ":%02d", g_TempAlarm.second);
                LCD_ShowString(133, 50, 200, 16, 16, (u8*)buffer);
            } else {
                // 整体高亮但未选择具体编辑项
                POINT_COLOR = RED;
                BACK_COLOR = GRAY;
                LCD_Fill(100, 50, 180, 66, GRAY);
                sprintf(buffer, "%02d:%02d:%02d", g_TempAlarm.hour, g_TempAlarm.minute, g_TempAlarm.second);
                LCD_ShowString(100, 50, 200, 16, 16, (u8*)buffer);
            }
        } else {
            // 选中但未编辑
            POINT_COLOR = RED;
            BACK_COLOR = BLACK;
            LCD_DrawRectangle(100, 50, 180, 66);
            sprintf(buffer, "%02d:%02d:%02d", g_TempAlarm.hour, g_TempAlarm.minute, g_TempAlarm.second);
            LCD_ShowString(100, 50, 200, 16, 16, (u8*)buffer);
        }
    } else {
        POINT_COLOR = GRAY;
        BACK_COLOR = BLACK;
        sprintf(buffer, "%02d:%02d:%02d", g_TempAlarm.hour, g_TempAlarm.minute, g_TempAlarm.second);
    LCD_ShowString(100, 50, 200, 16, 16, (u8*)buffer);
    }
    
    // 显示重复设置 (g_EditingField == 2)
    POINT_COLOR = GRAY;
    LCD_ShowString(20, 80, 200, 16, 16, (u8*)"Repeat:");
    
    if (g_SelectedItem == 2) {
        if (g_EditingField == 2) {
            // 正在编辑重复
            POINT_COLOR = RED;
            BACK_COLOR = GRAY;
            LCD_Fill(100, 80, 220, 96, GRAY);
        } else {
            // 选中但未编辑
            POINT_COLOR = RED;
            BACK_COLOR = BLACK;
            LCD_DrawRectangle(100, 80, 220, 96);
        }
    } else {
        POINT_COLOR = GRAY;
        BACK_COLOR = BLACK;
    }
    
    buffer[0] = 0;
    if(g_TempAlarm.days == 0x7F) {
        sprintf(buffer, "Every day");
    } else if(g_TempAlarm.days == 0x1F) {
        sprintf(buffer, "Weekdays");
    } else if(g_TempAlarm.days == 0x60) {
        sprintf(buffer, "Weekend");
    } else {
        if(g_TempAlarm.days & 0x01) strcat(buffer, "Sun ");
        if(g_TempAlarm.days & 0x02) strcat(buffer, "Mon ");
        if(g_TempAlarm.days & 0x04) strcat(buffer, "Tue ");
        if(g_TempAlarm.days & 0x08) strcat(buffer, "Wed ");
        if(g_TempAlarm.days & 0x10) strcat(buffer, "Thu ");
        if(g_TempAlarm.days & 0x20) strcat(buffer, "Fri ");
        if(g_TempAlarm.days & 0x40) strcat(buffer, "Sat ");
    }
    LCD_ShowString(100, 80, 200, 16, 16, (u8*)buffer);
    
    // 显示名称设置 (g_EditingField == 3)
    POINT_COLOR = GRAY;
    LCD_ShowString(20, 110, 200, 16, 16, (u8*)"Name:");
    
    if (g_SelectedItem == 3) {
        if (g_EditingField == 3) {
            // 正在编辑名称
            POINT_COLOR = RED;
            BACK_COLOR = GRAY;
            LCD_Fill(100, 110, 220, 126, GRAY);
        } else {
            // 选中但未编辑
            POINT_COLOR = RED;
            BACK_COLOR = BLACK;
            LCD_DrawRectangle(100, 110, 220, 126);
        }
    } else {
        POINT_COLOR = GRAY;
        BACK_COLOR = BLACK;
    }
    LCD_ShowString(100, 110, 200, 16, 16, (u8*)g_TempAlarm.name);
    
    // 显示启用状态 (g_EditingField == 4)
    POINT_COLOR = GRAY;
    LCD_ShowString(20, 140, 200, 16, 16, (u8*)"Status:");
    
    if (g_SelectedItem == 4) {
        if (g_EditingField == 4) {
            // 正在编辑状态
            POINT_COLOR = RED;
            BACK_COLOR = GRAY;
            LCD_Fill(100, 140, 170, 156, GRAY);
        } else {
            // 选中但未编辑
            POINT_COLOR = RED;
            BACK_COLOR = BLACK;
            LCD_DrawRectangle(100, 140, 170, 156);
        }
    } else {
        POINT_COLOR = GRAY;
        BACK_COLOR = BLACK;
    }
    LCD_ShowString(100, 140, 200, 16, 16, (u8*)(g_TempAlarm.enabled ? "Enabled" : "Disabled"));
    
    // 显示保存按钮
    if (g_SelectedItem == 5) {
        // 选中项高亮显示
        POINT_COLOR = RED;
        BACK_COLOR = GRAY;
        LCD_Fill(20, 180, 110, 210, GRAY);
    } else {
        POINT_COLOR = RED;
        BACK_COLOR = BLACK;
    }
    LCD_DrawRectangle(20, 180, 110, 210);
    LCD_ShowString(40, 188, 200, 16, 16, (u8*)"SAVE");
    
    // 显示取消按钮
    if (g_SelectedItem == 0) {
        // 选中项高亮显示
    POINT_COLOR = RED;
        BACK_COLOR = GRAY;
        LCD_Fill(130, 180, 220, 210, GRAY);
    } else {
        POINT_COLOR = GRAY;
        BACK_COLOR = BLACK;
    }
    LCD_DrawRectangle(130, 180, 220, 210);
    LCD_ShowString(150, 188, 200, 16, 16, (u8*)"CANCEL");
    
    // 恢复默认背景色
    BACK_COLOR = BLACK;
    g_NeedRefresh = 0;
    g_CurrentAlarmIndex = index;
}

// 绘制时间设置页面
void Clock_DrawTimeSet(DateTime* time)
{
    char buffer[30];
    static const char* weekdays[] = {"Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"};
    
    // 清屏，使用黑色背景
    LCD_Clear(BLACK);
    BACK_COLOR = BLACK;
    
    // 显示标题
    POINT_COLOR = RED;
    LCD_ShowString(70, 20, 200, 24, 24, (u8*)"TIME SETTINGS");
    
    // 只在首次进入页面时初始化临时时间
    if (g_FirstTimeEnter) {
        g_TempDateTime = *time;
        g_FirstTimeEnter = 0;
        printf("Loading current time: %02d:%02d:%02d, Date: %04d-%02d-%02d\r\n", 
               g_TempDateTime.hour, g_TempDateTime.minute, g_TempDateTime.second,
               g_TempDateTime.year, g_TempDateTime.month, g_TempDateTime.day);
    }
    
    // 显示年月日
    POINT_COLOR = GRAY;
    LCD_ShowString(40, 60, 200, 16, 16, (u8*)"Date:");
    
    // 根据编辑模式显示日期
    if (g_TimeEditMode == 4) {
        // 编辑年份
        POINT_COLOR = RED;
        sprintf(buffer, "%04d", g_TempDateTime.year);
        LCD_Fill(100, 60, 132, 76, GRAY);
        BACK_COLOR = GRAY;
        LCD_ShowString(100, 60, 32, 16, 16, (u8*)buffer);
        
        // 显示月日
        POINT_COLOR = GRAY;
        BACK_COLOR = BLACK;
        sprintf(buffer, "-%02d-%02d", g_TempDateTime.month, g_TempDateTime.day);
        LCD_ShowString(132, 60, 200, 16, 16, (u8*)buffer);
    } else if (g_TimeEditMode == 5) {
        // 编辑月份
        POINT_COLOR = GRAY;
        BACK_COLOR = BLACK;
        sprintf(buffer, "%04d-", g_TempDateTime.year);
        LCD_ShowString(100, 60, 200, 16, 16, (u8*)buffer);
        
        // 高亮显示月份
        POINT_COLOR = RED;
        BACK_COLOR = GRAY;
        sprintf(buffer, "%02d", g_TempDateTime.month);
        LCD_Fill(133, 60, 148, 76, GRAY);
        LCD_ShowString(133, 60, 15, 16, 16, (u8*)buffer);
        
        // 显示日期
        POINT_COLOR = GRAY;
        BACK_COLOR = BLACK;
        sprintf(buffer, "-%02d", g_TempDateTime.day);
        LCD_ShowString(148, 60, 200, 16, 16, (u8*)buffer);
    } else if (g_TimeEditMode == 6) {
        // 编辑日期
        POINT_COLOR = GRAY;
        BACK_COLOR = BLACK;
        sprintf(buffer, "%04d-%02d-", g_TempDateTime.year, g_TempDateTime.month);
        LCD_ShowString(100, 60, 200, 16, 16, (u8*)buffer);
        
        // 高亮显示日期
        POINT_COLOR = RED;
        BACK_COLOR = GRAY;
        sprintf(buffer, "%02d", g_TempDateTime.day);
        LCD_Fill(151, 60, 166, 76, GRAY);
        LCD_ShowString(151, 60, 15, 16, 16, (u8*)buffer);
    } else {
        if (g_SelectedItem == 3) {
            // 选中状态
            POINT_COLOR = RED;
            LCD_DrawRectangle(100, 60, 180, 76);
        } else {
            POINT_COLOR = GRAY;
        }
        BACK_COLOR = BLACK;
        sprintf(buffer, "%04d-%02d-%02d", g_TempDateTime.year, g_TempDateTime.month, g_TempDateTime.day);
        LCD_ShowString(100, 60, 200, 16, 16, (u8*)buffer);
    }
    
    // 显示星期
    POINT_COLOR = GRAY;
    BACK_COLOR = BLACK;
    sprintf(buffer, "Weekday: %s", weekdays[g_TempDateTime.week]);
    LCD_ShowString(80, 90, 200, 16, 16, (u8*)buffer);
    
    // 显示时间设置区域
    POINT_COLOR = GRAY;
    LCD_ShowString(40, 120, 200, 16, 16, (u8*)"Time:");
    
    // 根据编辑模式显示时间
    if (g_TimeEditMode == 1) {
        // 编辑小时
        POINT_COLOR = RED;
        BACK_COLOR = GRAY;
        sprintf(buffer, "%02d", g_TempDateTime.hour);
        LCD_Fill(100, 120, 115, 136, GRAY);
        LCD_ShowString(100, 120, 15, 16, 16, (u8*)buffer);
        
        // 显示分钟和秒钟
        POINT_COLOR = GRAY;
        BACK_COLOR = BLACK;
        sprintf(buffer, ":%02d:%02d", g_TempDateTime.minute, g_TempDateTime.second);
        LCD_ShowString(115, 120, 200, 16, 16, (u8*)buffer);
    } else if (g_TimeEditMode == 2) {
        // 编辑分钟
        POINT_COLOR = GRAY;
        BACK_COLOR = BLACK;
        sprintf(buffer, "%02d:", g_TempDateTime.hour);
        LCD_ShowString(100, 120, 200, 16, 16, (u8*)buffer);
        
        // 高亮显示分钟
        POINT_COLOR = RED;
        BACK_COLOR = GRAY;
        sprintf(buffer, "%02d", g_TempDateTime.minute);
        LCD_Fill(118, 120, 133, 136, GRAY);
        LCD_ShowString(118, 120, 15, 16, 16, (u8*)buffer);
        
        // 显示秒钟
        POINT_COLOR = GRAY;
        BACK_COLOR = BLACK;
        sprintf(buffer, ":%02d", g_TempDateTime.second);
        LCD_ShowString(133, 120, 200, 16, 16, (u8*)buffer);
    } else if (g_TimeEditMode == 3) {
        // 编辑秒钟
        POINT_COLOR = GRAY;
        BACK_COLOR = BLACK;
        sprintf(buffer, "%02d:%02d:", g_TempDateTime.hour, g_TempDateTime.minute);
        LCD_ShowString(100, 120, 200, 16, 16, (u8*)buffer);
        
        // 高亮显示秒钟
        POINT_COLOR = RED;
        BACK_COLOR = GRAY;
        sprintf(buffer, "%02d", g_TempDateTime.second);
        LCD_Fill(136, 120, 151, 136, GRAY);
        LCD_ShowString(136, 120, 15, 16, 16, (u8*)buffer);
    } else {
        // 未进入编辑模式，普通显示
        if (g_SelectedItem == 0) {
            // 选中状态
            POINT_COLOR = RED;
            LCD_DrawRectangle(100, 120, 180, 136);
        } else {
            POINT_COLOR = GRAY;
        }
        BACK_COLOR = BLACK;
        sprintf(buffer, "%02d:%02d:%02d", g_TempDateTime.hour, g_TempDateTime.minute, g_TempDateTime.second);
        LCD_ShowString(100, 120, 200, 16, 16, (u8*)buffer);
    }
    
    // 显示保存按钮
    if (g_SelectedItem == 1) {
        POINT_COLOR = RED;
        LCD_Fill(20, 160, 100, 190, GRAY);
        BACK_COLOR = GRAY;
    } else {
        POINT_COLOR = RED;
        BACK_COLOR = BLACK;
    }
    LCD_DrawRectangle(20, 160, 100, 190);
    LCD_ShowString(40, 168, 200, 16, 16, (u8*)"SAVE");
    
    // 显示取消按钮
    if (g_SelectedItem == 2) {
        POINT_COLOR = RED;
        LCD_Fill(140, 160, 220, 190, GRAY);
        BACK_COLOR = GRAY;
    } else {
        POINT_COLOR = GRAY;
        BACK_COLOR = BLACK;
    }
    LCD_DrawRectangle(140, 160, 220, 190);
    LCD_ShowString(160, 168, 200, 16, 16, (u8*)"CANCEL");
    
    // 恢复默认背景色
    BACK_COLOR = BLACK;
    g_NeedRefresh = 0;
}

// 只刷新变化的时间部分
void Clock_RefreshTimeDigit(DateTime* time)
{
    char buffer[30];
    POINT_COLOR = RED;
    BACK_COLOR = BLACK;
    
    // 使用更宽的间距，防止字符重叠
    // 小时: 60-84   冒号1: 84-96   分钟: 96-120   冒号2: 120-132   秒: 132-156
    
    // 检查秒数是否变化
    if (g_LastSecond != time->second) {
        sprintf(buffer, "%02d", time->second);
        // 清除原区域，扩大范围确保完全清除
        LCD_Fill(132, 110, 156, 134, BLACK);
        LCD_ShowString(132, 110, 40, 24, 24, (u8*)buffer);
        g_LastSecond = time->second;
    }
    
    // 检查分钟是否变化
    if (g_LastMinute != time->minute) {
        sprintf(buffer, "%02d", time->minute);
        // 清除原区域
        LCD_Fill(96, 110, 120, 134, BLACK);
        LCD_ShowString(96, 110, 40, 24, 24, (u8*)buffer);
        g_LastMinute = time->minute;
        
        // 确保分钟和秒之间的冒号显示
        // 先清除冒号区域
        LCD_Fill(120, 110, 132, 134, BLACK);
        LCD_ShowString(120, 110, 12, 24, 24, (u8*)":");
    }
    
    // 检查小时是否变化
    if (g_LastHour != time->hour) {
        sprintf(buffer, "%02d", time->hour);
        // 清除原区域
        LCD_Fill(60, 110, 84, 134, BLACK);
        LCD_ShowString(60, 110, 40, 24, 24, (u8*)buffer);
        g_LastHour = time->hour;
        
        // 确保小时和分钟之间的冒号显示
        // 先清除冒号区域
        LCD_Fill(84, 110, 96, 134, BLACK);
        LCD_ShowString(84, 110, 12, 24, 24, (u8*)":");
    }
    
    // 检查日期是否变化
    if (g_LastDay != time->day || g_LastMonth != time->month || g_LastYear != time->year) {
        POINT_COLOR = GRAY;
        sprintf(buffer, "%04d-%02d-%02d", time->year, time->month, time->day);
        LCD_ShowString(70, 50, 200, 16, 16, (u8*)buffer);
        g_LastDay = time->day;
        g_LastMonth = time->month;
        g_LastYear = time->year;
    }
    
    // 检查星期是否变化
    if (g_LastWeek != time->week) {
        static const char* weekdays[] = {"Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"};
        POINT_COLOR = GRAY;
        LCD_ShowString(85, 75, 200, 16, 16, (u8*)weekdays[time->week]);
        g_LastWeek = time->week;
    }
}

// 处理按键输入
void Clock_ProcessKey(uint8_t key)
{
    switch(key) {
        case KEY1_PRESSED:  // PA15 - 页面切换/确认按钮
            switch(g_CurrentPage) {
                case PAGE_CLOCK:
                    // 在时钟页面，根据选项执行不同操作
                    if (g_SelectedItem == 0) {
                        // 切换到闹钟列表
                        Clock_ChangePage(PAGE_ALARM_LIST);
                    } else if (g_SelectedItem == 1) {
                        // 进入时间设置
                        SelectCallback_SetTime();
                    }
                    break;
                    
                case PAGE_ALARM_LIST:
                    // 在闹钟列表页面，确认选择
                    if (g_SelectedItem == 0) {
                        // 返回时钟页面
                        SelectCallback_GotoClock();
                    } else if (g_SelectedItem == g_AlarmManager.count + 1) {
                        // 添加闹钟
                        SelectCallback_AddAlarm();
                    } else if (g_SelectedItem >= 1 && g_SelectedItem <= g_AlarmManager.count) {
                        // 编辑闹钟
                        SelectCallback_EditAlarm(g_SelectedItem - 1);
                    }
                    break;
                    
                case PAGE_ALARM_EDIT:
                    // 在闹钟编辑页面，确认选择或进入/退出编辑模式
                    if (g_SelectedItem == 0) {
                        // 取消编辑，放弃所有更改
                        g_EditingField = 0;
                        g_TimeEditMode = 0;
                        SelectCallback_CancelEdit();
                    } else if (g_SelectedItem == 5) {
                        // 保存闹钟
                        g_EditingField = 0;
                        g_TimeEditMode = 0;
                        SelectCallback_SaveAlarm();
                    } else if (g_SelectedItem >= 1 && g_SelectedItem <= 4) {
                        // 处理编辑字段的逻辑
                        if (g_EditingField == 0) {
                            // 进入编辑模式
                            g_EditingField = g_SelectedItem;
                            g_TimeEditMode = 0; // 重置时间编辑模式
                            g_NeedRefresh = 1;
                        } else if (g_EditingField == g_SelectedItem) {
                            // 当前已在编辑此字段
                            if (g_EditingField == 1) { // 时间编辑字段
                                if (g_TimeEditMode == 0) {
                                    // 进入小时编辑模式
                                    g_TimeEditMode = 1;
                                } else if (g_TimeEditMode == 1) {
                                    // 从小时编辑切换到分钟编辑
                                    g_TimeEditMode = 2;
                                } else if (g_TimeEditMode == 2) {
                                    // 完成时间编辑，退出编辑模式
                                    g_TimeEditMode = 0;
                                    g_EditingField = 0;
                                }
                                g_NeedRefresh = 1;
                            } else {
                                // 对于其他字段，退出编辑模式
                                g_EditingField = 0;
                                g_NeedRefresh = 1;
                            }
                        } else {
                            // 切换到编辑其他字段
                            g_EditingField = g_SelectedItem;
                            g_TimeEditMode = 0; // 重置时间编辑模式
                            g_NeedRefresh = 1;
                        }
                    }
                    break;
                    
                case PAGE_TIME_SET:
                    // 在时间设置页面处理确认按钮
                    if (g_SelectedItem == 0) {
                        // 当前在时间项上
                        if (g_TimeEditMode == 0) {
                            // 进入小时编辑模式
                            g_TimeEditMode = 1;
                        } else if (g_TimeEditMode == 1) {
                            // 从小时编辑切换到分钟编辑
                            g_TimeEditMode = 2;
                        } else if (g_TimeEditMode == 2) {
                            // 从分钟编辑切换到秒钟编辑
                            g_TimeEditMode = 3;
                        } else if (g_TimeEditMode == 3) {
                            // 完成时间编辑，退出编辑模式
                            g_TimeEditMode = 0;
                        }
                        g_NeedRefresh = 1;
                    } else if (g_SelectedItem == 3) {
                        // 当前在日期项上
                        if (g_TimeEditMode == 0) {
                            // 进入年份编辑模式
                            g_TimeEditMode = 4;
                        } else if (g_TimeEditMode == 4) {
                            // 从年份编辑切换到月份编辑
                            g_TimeEditMode = 5;
                        } else if (g_TimeEditMode == 5) {
                            // 从月份编辑切换到日期编辑
                            g_TimeEditMode = 6;
                        } else if (g_TimeEditMode == 6) {
                            // 完成日期编辑，退出编辑模式
                            g_TimeEditMode = 0;
                            // 自动计算星期几
                            g_TempDateTime.week = GetDayOfWeek(g_TempDateTime.year, g_TempDateTime.month, g_TempDateTime.day);
                        }
                        g_NeedRefresh = 1;
                    } else if (g_SelectedItem == 1) {
                        // 保存设置
                        SelectCallback_SaveTime();
                    } else if (g_SelectedItem == 2) {
                        // 取消设置
                        SelectCallback_CancelTimeSet();
                    }
                    break;
            }
            break;
            
        case KEY2_PRESSED:  // PC5 - 后退/递减按钮
            switch(g_CurrentPage) {
                case PAGE_TIME_SET:
                    // 在编辑模式下使用PC5按键递减值
                    if (g_TimeEditMode == 1) {
                        // 递减小时
                        if (g_TempDateTime.hour == 0)
                            g_TempDateTime.hour = 23;
                        else
                            g_TempDateTime.hour--;
                        printf("Decreasing hour: %02d\r\n", g_TempDateTime.hour);
                        g_NeedRefresh = 1;
                    } else if (g_TimeEditMode == 2) {
                        // 递减分钟
                        if (g_TempDateTime.minute == 0)
                            g_TempDateTime.minute = 59;
                        else
                            g_TempDateTime.minute--;
                        printf("Decreasing minute: %02d\r\n", g_TempDateTime.minute);
                        g_NeedRefresh = 1;
                    } else if (g_TimeEditMode == 3) {
                        // 递减秒钟
                        if (g_TempDateTime.second == 0)
                            g_TempDateTime.second = 59;
                        else
                            g_TempDateTime.second--;
                        printf("Decreasing second: %02d\r\n", g_TempDateTime.second);
                        g_NeedRefresh = 1;
                    } else if (g_TimeEditMode == 4) {
                        // 递减年份
                        if (g_TempDateTime.year == 2000)
                            g_TempDateTime.year = 2099;
                        else
                            g_TempDateTime.year--;
                        printf("Decreasing year: %04d\r\n", g_TempDateTime.year);
                        g_NeedRefresh = 1;
                    } else if (g_TimeEditMode == 5) {
                        // 递减月份
                        if (g_TempDateTime.month == 1)
                            g_TempDateTime.month = 12;
                        else
                            g_TempDateTime.month--;
                        
                        // 检查日期是否需要调整
                        uint8_t max_days = g_MonthDays[g_TempDateTime.month - 1];
                        if (g_TempDateTime.month == 2 && IsLeapYear(g_TempDateTime.year)) {
                            max_days = 29;
                        }
                        if (g_TempDateTime.day > max_days) {
                            g_TempDateTime.day = max_days;
                        }
                        
                        printf("Decreasing month: %02d\r\n", g_TempDateTime.month);
                        g_NeedRefresh = 1;
                    } else if (g_TimeEditMode == 6) {
                        // 递减日期
                        uint8_t max_days = g_MonthDays[g_TempDateTime.month - 1];
                        if (g_TempDateTime.month == 2 && IsLeapYear(g_TempDateTime.year)) {
                            max_days = 29;
                        }
                        
                        if (g_TempDateTime.day == 1)
                            g_TempDateTime.day = max_days;
                        else
                            g_TempDateTime.day--;
                        
                        printf("Decreasing day: %02d\r\n", g_TempDateTime.day);
                        g_NeedRefresh = 1;
                    }
                    break;
                    
                // 如需要，可以为其他页面添加PC5按键处理逻辑
            }
            break;
            
        case KEY3_PRESSED:  // PA0 - 选择/修改按钮
            switch(g_CurrentPage) {
                case PAGE_CLOCK:
                    // 时钟页面循环选择按钮
                    g_SelectedItem = (g_SelectedItem + 1) % 2; // 0-1，共2项
                    g_NeedRefresh = 1;
                    break;
                    
                case PAGE_ALARM_LIST:
                    // 闹钟列表页面有多个选项：返回按钮、闹钟列表、添加按钮
                    g_SelectedItem = (g_SelectedItem + 1) % (g_AlarmManager.count + 2);
                    g_NeedRefresh = 1;
                    break;
                    
                case PAGE_ALARM_EDIT:
                    if (g_EditingField == 0) {
                        // 未处于编辑状态，选择不同项目
                        g_SelectedItem = (g_SelectedItem + 1) % 6; // 0-5，共6项
                        g_NeedRefresh = 1;
                    } else {
                        // 处于编辑状态，修改当前项目值
                        switch(g_EditingField) {
                            case 1: // 编辑时间
                                if (g_TimeEditMode == 1) {
                                    // 编辑小时
                                    g_TempAlarm.hour = (g_TempAlarm.hour + 1) % 24;
                                    printf("Editing hour: %02d\r\n", g_TempAlarm.hour);
                                } else if (g_TimeEditMode == 2) {
                                    // 编辑分钟
                                    g_TempAlarm.minute = (g_TempAlarm.minute + 1) % 60;
                                    printf("Editing minute: %02d\r\n", g_TempAlarm.minute);
                                }
                                g_NeedRefresh = 1;
                                break;
                                
                            case 2: // 编辑重复
                                // 在几种预设值之间切换
                                if (g_TempAlarm.days == 0x7F) g_TempAlarm.days = 0x1F; // 每天 -> 工作日
                                else if (g_TempAlarm.days == 0x1F) g_TempAlarm.days = 0x60; // 工作日 -> 周末
                                else g_TempAlarm.days = 0x7F; // 其他 -> 每天
                                g_NeedRefresh = 1;
                                break;
                                
                            case 3: // 编辑名称
                                // 在几种预设名称之间切换
                                if (strcmp(g_TempAlarm.name, "Morning") == 0) 
                                    strcpy(g_TempAlarm.name, "Noon");
                                else if (strcmp(g_TempAlarm.name, "Noon") == 0) 
                                    strcpy(g_TempAlarm.name, "Evening");
                                else if (strcmp(g_TempAlarm.name, "Evening") == 0) 
                                    strcpy(g_TempAlarm.name, "Night");
                                else 
                                    strcpy(g_TempAlarm.name, "Morning");
                                g_NeedRefresh = 1;
                                break;
                                
                            case 4: // 编辑状态
                                // 切换启用/禁用状态
                                g_TempAlarm.enabled = !g_TempAlarm.enabled;
                                g_NeedRefresh = 1;
                                break;
                        }
                    }
                    break;
                    
                case PAGE_TIME_SET:
                    if (g_TimeEditMode == 0) {
                        // 未处于编辑状态，在四个选项之间循环（时间、保存、取消、日期）
                        g_SelectedItem = (g_SelectedItem + 1) % 4; // 0-3，共4项
                        g_NeedRefresh = 1;
                    } else {
                        // 处于编辑状态，修改当前值
                        if (g_TimeEditMode == 1) {
                            // 编辑小时
                            g_TempDateTime.hour = (g_TempDateTime.hour + 1) % 24;
                            printf("Setting hour: %02d\r\n", g_TempDateTime.hour);
                        } else if (g_TimeEditMode == 2) {
                            // 编辑分钟
                            g_TempDateTime.minute = (g_TempDateTime.minute + 1) % 60;
                            printf("Setting minute: %02d\r\n", g_TempDateTime.minute);
                        } else if (g_TimeEditMode == 3) {
                            // 编辑秒钟
                            g_TempDateTime.second = (g_TempDateTime.second + 1) % 60;
                            printf("Setting second: %02d\r\n", g_TempDateTime.second);
                        } else if (g_TimeEditMode == 4) {
                            // 编辑年份
                            g_TempDateTime.year = (g_TempDateTime.year < 2099) ? g_TempDateTime.year + 1 : 2000;
                            printf("Setting year: %04d\r\n", g_TempDateTime.year);
                        } else if (g_TimeEditMode == 5) {
                            // 编辑月份
                            g_TempDateTime.month = (g_TempDateTime.month % 12) + 1; // 1-12
                            printf("Setting month: %02d\r\n", g_TempDateTime.month);
                            
                            // 检查日期是否需要调整（例如，如果是2月，日期可能需要限制在28/29天）
                            uint8_t max_days = g_MonthDays[g_TempDateTime.month - 1];
                            if (g_TempDateTime.month == 2 && IsLeapYear(g_TempDateTime.year)) {
                                max_days = 29;
                            }
                            if (g_TempDateTime.day > max_days) {
                                g_TempDateTime.day = max_days;
                            }
                        } else if (g_TimeEditMode == 6) {
                            // 编辑日期
                            uint8_t max_days = g_MonthDays[g_TempDateTime.month - 1];
                            if (g_TempDateTime.month == 2 && IsLeapYear(g_TempDateTime.year)) {
                                max_days = 29;
                            }
                            g_TempDateTime.day = (g_TempDateTime.day % max_days) + 1; // 1-max_days
                            printf("Setting day: %02d\r\n", g_TempDateTime.day);
                        }
                        g_NeedRefresh = 1;
                    }
                    break;
            }
            break;
    }
}



// 选择回调：进入闹钟列表页面
static void SelectCallback_GotoAlarmList(void)
{
    Clock_ChangePage(PAGE_ALARM_LIST);
}

// 选择回调：返回时钟页面
static void SelectCallback_GotoClock(void)
{
    Clock_ChangePage(PAGE_CLOCK);
}

// 选择回调：添加闹钟
static void SelectCallback_AddAlarm(void)
{
    g_CurrentAlarmIndex = g_AlarmManager.count;
    g_FirstTimeEnter = 1; // 重置标志，确保正确初始化新闹钟
    Clock_ChangePage(PAGE_ALARM_EDIT);
}

// 选择回调：编辑闹钟
static void SelectCallback_EditAlarm(uint8_t index)
{
    g_CurrentAlarmIndex = index;
    g_FirstTimeEnter = 1; // 重置标志，确保重新初始化临时闹钟
    Clock_ChangePage(PAGE_ALARM_EDIT);
}

// 选择回调：删除闹钟
static void SelectCallback_DeleteAlarm(uint8_t index)
{
    if(Alarm_Delete(&g_AlarmManager, index)) {
        Clock_ChangePage(PAGE_ALARM_LIST);
    }
}

// 选择回调：保存闹钟
static void SelectCallback_SaveAlarm(void)
{
    printf("Saving alarm settings: Time=%02d:%02d:%02d, Status=%d\r\n", 
           g_TempAlarm.hour, g_TempAlarm.minute, g_TempAlarm.second, g_TempAlarm.enabled);
    
    // 保存当前编辑的临时闹钟
    if (g_CurrentAlarmIndex < g_AlarmManager.count) {
        // 更新现有闹钟
        g_AlarmManager.alarms[g_CurrentAlarmIndex] = g_TempAlarm;
        printf("Updated alarm[%d] successfully\r\n", g_CurrentAlarmIndex);
    } else {
        // 添加新闹钟
        if (g_AlarmManager.count < 10) {
            g_AlarmManager.alarms[g_AlarmManager.count] = g_TempAlarm;
            g_AlarmManager.count++;
            printf("Added new alarm successfully, current count: %d\r\n", g_AlarmManager.count);
        }
    }
    
    // 退出编辑模式
    g_EditingField = 0;
    g_TimeEditMode = 0;
    g_FirstTimeEnter = 1; // 重置标志，以便下次编辑时重新初始化
    
    // 返回闹钟列表
    Clock_ChangePage(PAGE_ALARM_LIST);
}

// 选择回调：取消编辑
static void SelectCallback_CancelEdit(void)
{
    // 退出编辑模式
    g_EditingField = 0;
    g_TimeEditMode = 0;
    g_FirstTimeEnter = 1; // 重置标志，以便下次编辑时重新初始化
    
    Clock_ChangePage(PAGE_ALARM_LIST);
}

// 选择回调：设置系统时间
static void SelectCallback_SetTime(void)
{
    g_FirstTimeEnter = 1; // 重置标志，确保载入当前时间
    Clock_ChangePage(PAGE_TIME_SET);
}

// 选择回调：保存系统时间设置
static void SelectCallback_SaveTime(void)
{
    printf("Saving system time: %02d:%02d:%02d\r\n", 
           g_TempDateTime.hour, g_TempDateTime.minute, g_TempDateTime.second);
    
    // 保存设置的时间
    RTC_SetTime(&g_TempDateTime);
    
    // 退出编辑模式
    g_TimeEditMode = 0;
    g_EditingField = 0;
    g_FirstTimeEnter = 1; // 重置标志
    
    // 返回时钟页面
    Clock_ChangePage(PAGE_CLOCK);
}

// 选择回调：取消系统时间设置
static void SelectCallback_CancelTimeSet(void)
{
    // 退出编辑模式
    g_TimeEditMode = 0;
    g_EditingField = 0;
    g_FirstTimeEnter = 1; // 重置标志
    
    // 返回时钟页面
    Clock_ChangePage(PAGE_CLOCK);
}

// 根据年月日计算星期几（基于Zeller公式）
uint8_t GetDayOfWeek(uint16_t year, uint8_t month, uint8_t day)
{
    if (month == 1 || month == 2) {
        month += 12;
        year--;
    }

    uint16_t century = year / 100;
    year = year % 100;

    uint8_t week = (day + 13 * (month + 1) / 5 + year + year / 4 + century / 4 - 2 * century) % 7;

    // 转换为0-6（周日-周六）
    if (week == 0) week = 7;
    return (week - 1);
}

// ==================== 新增的圆盘时钟绘制函数 ====================

// 绘制完整的模拟时钟
void Clock_DrawAnalogClock(DateTime* time)
{
    static uint8_t first_draw = 1;

    if (first_draw || g_NeedRefresh) {
        // 清屏并绘制表盘
        LCD_Clear(WHITE);

        // 添加调试信息 - 在左上角显示"ANALOG CLOCK"确认函数被调用
        POINT_COLOR = RED;
        BACK_COLOR = WHITE;
        LCD_ShowString(10, 10, 200, 16, 16, (u8*)"ANALOG CLOCK");

        Clock_DrawClockFace();
        Clock_DrawDateInfo(time);
        first_draw = 0;
        g_NeedRefresh = 0;
    }

    // 清除旧指针（如果时间发生变化）
    if (g_LastDrawTime.hour != time->hour ||
        g_LastDrawTime.minute != time->minute ||
        g_LastDrawTime.second != time->second) {
        Clock_ClearHands(&g_LastDrawTime);
    }

    // 绘制新指针
    Clock_DrawHands(time);

    // 更新上次绘制时间
    g_LastDrawTime = *time;
}

// 绘制时钟表盘
void Clock_DrawClockFace(void)
{
    // 添加调试信息 - 显示表盘中心坐标
    char debug_buffer[50];
    POINT_COLOR = BLUE;
    BACK_COLOR = WHITE;
    sprintf(debug_buffer, "Center:(%d,%d) R:%d", CLOCK_CENTER_X, CLOCK_CENTER_Y, CLOCK_RADIUS);
    LCD_ShowString(10, 30, 200, 16, 16, (u8*)debug_buffer);

    // 绘制外圈
    POINT_COLOR = BLACK;
    LCD_Draw_Circle(CLOCK_CENTER_X, CLOCK_CENTER_Y, CLOCK_RADIUS);
    LCD_Draw_Circle(CLOCK_CENTER_X, CLOCK_CENTER_Y, CLOCK_RADIUS - 1);

    // 绘制小时刻度和数字
    Clock_DrawHourMarks();
    Clock_DrawNumbers();

    // 绘制分钟刻度
    Clock_DrawMinuteMarks();

    // 绘制中心点
    Clock_DrawCenterDot();
}

// 绘制小时刻度
void Clock_DrawHourMarks(void)
{
    int i;
    float angle;
    int x1, y1, x2, y2;

    POINT_COLOR = BLACK;

    for(i = 0; i < 12; i++) {
        angle = i * 30 * M_PI / 180;

        // 外端点
        x1 = CLOCK_CENTER_X + (CLOCK_RADIUS - 5) * sin(angle);
        y1 = CLOCK_CENTER_Y - (CLOCK_RADIUS - 5) * cos(angle);

        // 内端点
        x2 = CLOCK_CENTER_X + (CLOCK_RADIUS - 5 - HOUR_MARK_LEN) * sin(angle);
        y2 = CLOCK_CENTER_Y - (CLOCK_RADIUS - 5 - HOUR_MARK_LEN) * cos(angle);

        // 绘制粗刻度线
        DrawThickLine(x1, y1, x2, y2, BLACK, 2);
    }
}

// 绘制分钟刻度
void Clock_DrawMinuteMarks(void)
{
    int i;
    float angle;
    int x1, y1, x2, y2;

    POINT_COLOR = GRAY;

    for(i = 0; i < 60; i++) {
        // 跳过小时刻度位置
        if (i % 5 == 0) continue;

        angle = i * 6 * M_PI / 180;

        // 外端点
        x1 = CLOCK_CENTER_X + (CLOCK_RADIUS - 5) * sin(angle);
        y1 = CLOCK_CENTER_Y - (CLOCK_RADIUS - 5) * cos(angle);

        // 内端点
        x2 = CLOCK_CENTER_X + (CLOCK_RADIUS - 5 - MINUTE_MARK_LEN) * sin(angle);
        y2 = CLOCK_CENTER_Y - (CLOCK_RADIUS - 5 - MINUTE_MARK_LEN) * cos(angle);

        // 绘制细刻度线
        LCD_DrawLine(x1, y1, x2, y2);
    }
}

// 绘制时钟数字
void Clock_DrawNumbers(void)
{
    int i;
    float angle;
    int x, y;
    char num_str[3];

    POINT_COLOR = BLACK;
    BACK_COLOR = WHITE;

    for(i = 1; i <= 12; i++) {
        angle = i * 30 * M_PI / 180;

        // 计算数字位置（稍微向内一些）
        x = CLOCK_CENTER_X + (CLOCK_RADIUS - 20) * sin(angle) - 6;
        y = CLOCK_CENTER_Y - (CLOCK_RADIUS - 20) * cos(angle) - 8;

        sprintf(num_str, "%d", i);
        LCD_ShowString(x, y, 20, 16, 16, (u8*)num_str);
    }
}

// 绘制指针
void Clock_DrawHand(int16_t angle, uint8_t length, uint16_t color, uint8_t thickness)
{
    float rad = angle * M_PI / 180.0;
    int16_t x = CLOCK_CENTER_X + length * sin(rad);
    int16_t y = CLOCK_CENTER_Y - length * cos(rad);

    if (thickness > 1) {
        DrawThickLine(CLOCK_CENTER_X, CLOCK_CENTER_Y, x, y, color, thickness);
    } else {
        POINT_COLOR = color;
        LCD_DrawLine(CLOCK_CENTER_X, CLOCK_CENTER_Y, x, y);
    }
}

// 重新实现Clock_DrawHands函数以支持新的圆盘时钟
void Clock_DrawHands(DateTime* time)
{
    // 计算指针角度
    int16_t hour_angle = ((time->hour % 12) * 30 + time->minute / 2);
    int16_t minute_angle = time->minute * 6;
    int16_t second_angle = time->second * 6;

    // 绘制时针（粗黑线）
    Clock_DrawHand(hour_angle, HOUR_HAND_LEN, BLACK, 3);

    // 绘制分针（中等蓝线）
    Clock_DrawHand(minute_angle, MINUTE_HAND_LEN, BLUE, 2);

    // 绘制秒针（细红线）
    Clock_DrawHand(second_angle, SECOND_HAND_LEN, RED, 1);

    // 重新绘制中心点（确保在指针之上）
    Clock_DrawCenterDot();
}

// 清除旧指针
void Clock_ClearHands(DateTime* last_time)
{
    if (last_time->year == 0) return; // 首次绘制，无需清除

    // 计算旧指针角度
    int16_t hour_angle = ((last_time->hour % 12) * 30 + last_time->minute / 2);
    int16_t minute_angle = last_time->minute * 6;
    int16_t second_angle = last_time->second * 6;

    // 用白色清除旧指针
    Clock_DrawHand(hour_angle, HOUR_HAND_LEN, WHITE, 3);
    Clock_DrawHand(minute_angle, MINUTE_HAND_LEN, WHITE, 2);
    Clock_DrawHand(second_angle, SECOND_HAND_LEN, WHITE, 1);

    // 重新绘制被清除的刻度和数字（简化版本）
    // 这里可以优化为只重绘被影响的部分
}

// 绘制中心圆点
void Clock_DrawCenterDot(void)
{
    POINT_COLOR = BLACK;
    LCD_Draw_Circle(CLOCK_CENTER_X, CLOCK_CENTER_Y, CENTER_DOT_RADIUS);
    LCD_Fill(CLOCK_CENTER_X - CENTER_DOT_RADIUS + 1,
             CLOCK_CENTER_Y - CENTER_DOT_RADIUS + 1,
             CLOCK_CENTER_X + CENTER_DOT_RADIUS - 1,
             CLOCK_CENTER_Y + CENTER_DOT_RADIUS - 1, BLACK);
}

// 绘制日期信息
void Clock_DrawDateInfo(DateTime* time)
{
    char buffer[50];

    POINT_COLOR = BLACK;
    BACK_COLOR = WHITE;

    // 简化显示 - 固定位置，确保可见
    sprintf(buffer, "%04d-%02d-%02d",
            time->year, time->month, time->day);
    LCD_ShowString(50, DATE_DISPLAY_Y, 200, 16, 16, (u8*)buffer);

    // 显示星期
    sprintf(buffer, "%s", GetWeekdayName(time->week));
    LCD_ShowString(50, DATE_DISPLAY_Y + 20, 200, 16, 16, (u8*)buffer);

    // 在表盘下方显示数字时间 - 固定位置
    POINT_COLOR = BLUE;
    sprintf(buffer, "%02d:%02d:%02d", time->hour, time->minute, time->second);
    LCD_ShowString(50, DIGITAL_TIME_Y, 100, 24, 24, (u8*)buffer);
}

// 计算角度
int16_t Clock_CalculateAngle(uint8_t value, uint8_t max_value)
{
    return (value * 360) / max_value;
}

// ==================== 辅助函数 ====================

// 绘制粗线条
void DrawThickLine(int16_t x1, int16_t y1, int16_t x2, int16_t y2, uint16_t color, uint8_t thickness)
{
    int16_t i, j;
    POINT_COLOR = color;

    for (i = -(thickness/2); i <= thickness/2; i++) {
        for (j = -(thickness/2); j <= thickness/2; j++) {
            LCD_DrawLine(x1 + i, y1 + j, x2 + i, y2 + j);
        }
    }
}

// 获取星期名称
const char* GetWeekdayName(uint8_t weekday)
{
    if (weekday < 7) {
        return weekday_names[weekday];
    }
    return "Unknown";
}

// 获取月份名称
const char* GetMonthName(uint8_t month)
{
    if (month >= 1 && month <= 12) {
        return month_names[month];
    }
    return "Unknown";
}

// 修改主显示函数以支持圆盘时钟
void Clock_Display(DateTime* time)
{
    // 根据当前页面显示不同内容
    switch(g_CurrentPage) {
        case PAGE_CLOCK:
            // 使用新的圆盘时钟显示
            Clock_DrawAnalogClock(time);
            break;
        case PAGE_ALARM_LIST:
            if(g_NeedRefresh) {
                Clock_DrawAlarmList();
            }
            break;
        case PAGE_ALARM_EDIT:
            if(g_NeedRefresh) {
                Clock_DrawAlarmEdit(g_CurrentAlarmIndex);
            }
            break;
        case PAGE_TIME_SET:
            if(g_NeedRefresh) {
                Clock_DrawTimeSet(time);
            }
            break;
    }
}