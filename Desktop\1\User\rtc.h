#ifndef __RTC_H
#define __RTC_H

#include "stm32f10x.h"
#include <stdio.h>

// 日期时间结构体
typedef struct {
    uint16_t year;   // 年份 (2000-2099)
    uint8_t month;   // 月份 (1-12)
    uint8_t day;     // 日期 (1-31)
    uint8_t week;    // 星期 (0-6, 0为星期日)
    uint8_t hour;    // 小时 (0-23)
    uint8_t minute;  // 分钟 (0-59)
    uint8_t second;  // 秒钟 (0-59)
    uint16_t millisecond; // 毫秒 (0-999)
} DateTime;

// 闹钟结构体
typedef struct {
    uint8_t hour;    // 小时 (0-23)
    uint8_t minute;  // 分钟 (0-59)
    uint8_t second;  // 秒钟 (0-59)
    uint8_t enabled; // 是否启用 (0-禁用, 1-启用)
    uint8_t days;    // 重复日 (bit0-bit6分别代表周日到周六)
    char name[20];   // 闹钟名称
} Alarm;

// 闹钟管理结构体
typedef struct {
    Alarm alarms[10];  // 最多10组闹钟
    uint8_t count;     // 当前闹钟数量
} AlarmManager;

// 函数声明
void RTC_Init(void);
void RTC_SetTime(DateTime* time);
void RTC_GetTime(DateTime* time);
void DateTime_Update(DateTime* time);
uint8_t IsLeapYear(uint16_t year);
void Alarm_Init(AlarmManager* manager);
uint8_t Alarm_Add(AlarmManager* manager, Alarm* alarm);
uint8_t Alarm_Delete(AlarmManager* manager, uint8_t index);
uint8_t Alarm_Update(AlarmManager* manager, uint8_t index, Alarm* alarm);
uint8_t Alarm_Check(AlarmManager* manager, DateTime* time);

// 全局变量声明
extern DateTime g_DateTime;
extern AlarmManager g_AlarmManager;
extern volatile uint32_t g_TimerCounter; // 毫秒计数器
extern const uint8_t g_MonthDays[]; // 每月天数数组

#endif /* __RTC_H */ 
