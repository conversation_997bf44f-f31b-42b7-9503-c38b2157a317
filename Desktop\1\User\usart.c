#include <usart.h>

// 接收缓冲区和状态变量定义
u8 USART_RX_BUF[USART_REC_LEN];  // 接收缓冲区
u16 USART_RX_STA = 0;            // 接收状态标记

/**
 * @brief  初始化USART1通信接口
 * @param  bound: 波特率值，如9600、115200等
 * @retval 无
 */
void uart1_init(u32 bound) {
  // 定义GPIO和USART初始化结构体变量
  GPIO_InitTypeDef GPIO_InitStructure;
  USART_InitTypeDef USART_InitStructure;
  NVIC_InitTypeDef NVIC_InitStructure;
  
  // 使能USART1和GPIOA时钟
  RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART1 | RCC_APB2Periph_GPIOA, ENABLE);
  
  // 配置USART1_TX (PA.9)为复用推挽输出
  GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9;
  GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;      // 复用推挽输出模式
  GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;    // IO口速度为50MHz
  GPIO_Init(GPIOA, &GPIO_InitStructure);               // 初始化GPIOA.9
  
  // 配置USART1_RX (PA.10)为浮空输入
  GPIO_InitStructure.GPIO_Pin = GPIO_Pin_10;
  GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN_FLOATING; // 浮空输入模式
  GPIO_Init(GPIOA, &GPIO_InitStructure);                // 初始化GPIOA.10
  
  // 配置USART1通信参数
  USART_InitStructure.USART_BaudRate = bound;                  // 设置波特率
  USART_InitStructure.USART_WordLength = USART_WordLength_8b;  // 数据位8位
  USART_InitStructure.USART_StopBits = USART_StopBits_1;       // 停止位1位
  USART_InitStructure.USART_Parity = USART_Parity_No;          // 无校验位
  USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None; // 无硬件流控
  USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx; // 收发模式
  
  USART_Init(USART1, &USART_InitStructure);  // 初始化USART1
  
  // 配置USART1接收中断
  NVIC_InitStructure.NVIC_IRQChannel = USART1_IRQn;
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 3;  // 抢占优先级3
  NVIC_InitStructure.NVIC_IRQChannelSubPriority = 3;         // 子优先级3
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;            // IRQ通道使能
  NVIC_Init(&NVIC_InitStructure);                            // 初始化NVIC
  
  // 使能USART1接收中断
  USART_ITConfig(USART1, USART_IT_RXNE, ENABLE);
  
  USART_Cmd(USART1, ENABLE);                 // 使能USART1
}

/**
 * @brief  USART1中断服务函数
 * @param  无
 * @retval 无
 */
void USART1_IRQHandler(void)
{
  u8 res;
  
  if(USART_GetITStatus(USART1, USART_IT_RXNE) != RESET)  // 接收中断
  {
    res = USART_ReceiveData(USART1);  // 读取接收到的数据
    
    if((USART_RX_STA & 0x8000) == 0)  // 接收未完成
    {
      if(USART_RX_STA & 0x4000)  // 接收到了0x0d（回车符）
      {
        if(res != 0x0a) // 不是0x0a（换行符）
        {
          USART_RX_BUF[USART_RX_STA & 0X3FFF] = res;  // 保存这个字符
          USART_RX_STA++;
          if(USART_RX_STA > (USART_REC_LEN-1))
            USART_RX_STA = 0;  // 溢出重新开始
          else 
            USART_RX_STA |= 0x8000;  // 接收完成
        }
        else
        {
          USART_RX_STA |= 0x8000;  // 接收完成了
        }
      }
      else  // 还没收到0x0d
      {
        if(res == 0x0d)
        {
          USART_RX_STA |= 0x4000;  // 接收到了0x0d
        }
        else
        {
          USART_RX_BUF[USART_RX_STA & 0X3FFF] = res;  // 存储数据
          USART_RX_STA++;
          
          if(USART_RX_STA > (USART_REC_LEN-1))
          {
            USART_RX_STA = 0;  // 接收数据错误，重新开始接收
          }
        }
      }
    }
  }
}

/**
 * @brief  通过USART发送字符串
 * @param  USARTx: 目标USART端口，如USART1
 * @param  str: 要发送的字符串指针
 * @retval 无
 */
void USART_SendString(USART_TypeDef *USARTx, char *str) {
    while (*str) {  // 遍历字符串直到遇到'\0'
        // 发送单个字符
        USART_SendData(USARTx, (uint8_t)(*str));
        
        // 等待发送缓冲区为空
        while (USART_GetFlagStatus(USARTx, USART_FLAG_TXE) == RESET);
        
        str++;  // 指向下一个字符
    }
    
    // 等待发送完成
    while (USART_GetFlagStatus(USARTx, USART_FLAG_TC) == RESET);
}

// 标准库重定向支持函数
#pragma import(__use_no_semihosting)             
struct __FILE { 
    int handle;
}; 
FILE __stdout;       

/**
 * @brief  重定向printf输出到USART1
 * @param  ch: 要输出的字符
 * @param  f: 文件指针
 * @retval 输出的字符
 */
int fputc(int ch, FILE *f) {
    // 发送字符到USART1
    USART1->DR = (u8) ch;
    
    // 等待发送完成
    while(USART_GetFlagStatus(USART1, USART_FLAG_TC) != SET);
    
    return ch;
}

// 避免使用半主机模式
void _sys_exit(int x) { 
    x = x;  // 空参数
}
