#ifndef __TOUCH_H
#define __TOUCH_H

#include "stm32f10x.h"
#include "sys.h"

// 触摸屏控制器
#define TP_CS_PIN     GPIO_Pin_4
#define TP_CS_PORT    GPIOB
#define TP_CS_CLK     RCC_APB2Periph_GPIOB

#define TP_DOUT_PIN   GPIO_Pin_5
#define TP_DOUT_PORT  GPIOB
#define TP_DOUT_CLK   RCC_APB2Periph_GPIOB

#define TP_DIN_PIN    GPIO_Pin_6
#define TP_DIN_PORT   GPIOB
#define TP_DIN_CLK    RCC_APB2Periph_GPIOB

#define TP_CLK_PIN    GPIO_Pin_7
#define TP_CLK_PORT   GPIOB
#define TP_CLK_CLK    RCC_APB2Periph_GPIOB

#define TP_IRQ_PIN    GPIO_Pin_8
#define TP_IRQ_PORT   GPIOB
#define TP_IRQ_CLK    RCC_APB2Periph_GPIOB

// 触摸屏IO操作函数
#define TP_CS_SET     GPIO_SetBits(TP_CS_PORT, TP_CS_PIN)
#define TP_CS_CLR     GPIO_ResetBits(TP_CS_PORT, TP_CS_PIN)

#define TP_DOUT       GPIO_ReadInputDataBit(TP_DOUT_PORT, TP_DOUT_PIN)

#define TP_DIN_SET    GPIO_SetBits(TP_DIN_PORT, TP_DIN_PIN)
#define TP_DIN_CLR    GPIO_ResetBits(TP_DIN_PORT, TP_DIN_PIN)

#define TP_CLK_SET    GPIO_SetBits(TP_CLK_PORT, TP_CLK_PIN)
#define TP_CLK_CLR    GPIO_ResetBits(TP_CLK_PORT, TP_CLK_PIN)

#define TP_IRQ        GPIO_ReadInputDataBit(TP_IRQ_PORT, TP_IRQ_PIN)

// 触摸屏参数
#define TP_PRES_DOWN  0x80  // 触屏被按下
#define TP_CATH_PRES  0x40  // 有按键按下

// 触摸屏校准参数
extern uint8_t  CMD_RDX;
extern uint8_t  CMD_RDY;

// 函数声明
void TP_Init(void);                       // 初始化
uint8_t TP_Scan(uint8_t mode);            // 扫描
void TP_Adjust(void);                     // 校准
uint16_t TP_Get_X(void);                  // 获取X坐标
uint16_t TP_Get_Y(void);                  // 获取Y坐标
uint8_t TP_Read_XY(uint16_t *x, uint16_t *y); // 读取坐标
uint8_t TP_Read_XY2(uint16_t *x, uint16_t *y); // 连续读取两次坐标

#endif /* __TOUCH_H */ 