Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    stm32f10x_adc.o(i.ADC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_bkp.o(i.BKP_DeInit) refers to stm32f10x_rcc.o(i.RCC_BackupResetCmd) for RCC_BackupResetCmd
    stm32f10x_can.o(i.CAN_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_can.o(i.CAN_GetITStatus) refers to stm32f10x_can.o(i.CheckITStatus) for CheckITStatus
    stm32f10x_cec.o(i.CEC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_dac.o(i.DAC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_flash.o(i.FLASH_EnableWriteProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) for FLASH_WaitForLastBank1Operation
    stm32f10x_flash.o(i.FLASH_EraseAllPages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus) for FLASH_GetReadOutProtectionStatus
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ErasePage) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramOptionByteData) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ReadOutProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_UserOptionByteConfig) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_pwr.o(i.PWR_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_spi.o(i.I2S_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI3_Config) for TI3_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI4_Config) for TI4_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_wwdg.o(i.WWDG_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    startup_stm32f10x_md.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(.text) for Reset_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.TIM3_IRQHandler) for TIM3_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f10x_md.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_md.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_md.o(.text) refers to startup_stm32f10x_md.o(HEAP) for Heap_Mem
    startup_stm32f10x_md.o(.text) refers to startup_stm32f10x_md.o(STACK) for Stack_Mem
    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    lcd.o(i.LCD_Clear) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_Clear) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_Clear) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_Color_Fill) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_Color_Fill) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_DisplayOff) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_DisplayOff) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_DisplayOff) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_DisplayOn) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_DisplayOn) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_DisplayOn) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_Display_Dir) refers to lcd.o(i.LCD_Scan_Dir) for LCD_Scan_Dir
    lcd.o(i.LCD_Display_Dir) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_DrawLine) refers to lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(i.LCD_DrawPoint) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_DrawPoint) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_DrawPoint) refers to lcd.o(.data) for POINT_COLOR
    lcd.o(i.LCD_DrawRectangle) refers to lcd.o(i.LCD_DrawLine) for LCD_DrawLine
    lcd.o(i.LCD_Draw_Circle) refers to lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(i.LCD_Fast_DrawPoint) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_Fast_DrawPoint) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_Fast_DrawPoint) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_Fill) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_Fill) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_Fill) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_Init) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    lcd.o(i.LCD_Init) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    lcd.o(i.LCD_Init) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    lcd.o(i.LCD_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    lcd.o(i.LCD_Init) refers to stm32f10x_gpio.o(i.GPIO_PinRemapConfig) for GPIO_PinRemapConfig
    lcd.o(i.LCD_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    lcd.o(i.LCD_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    lcd.o(i.LCD_Init) refers to timer.o(i.delay_ms) for delay_ms
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_ReadReg) for LCD_ReadReg
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_RD_DATA) for LCD_RD_DATA
    lcd.o(i.LCD_Init) refers to noretval__2printf.o(.text) for __2printf
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_WR_DATAX) for LCD_WR_DATAX
    lcd.o(i.LCD_Init) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_Init) refers to timer.o(i.delay_us) for delay_us
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_SSD_BackLightSet) for LCD_SSD_BackLightSet
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_Display_Dir) for LCD_Display_Dir
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    lcd.o(i.LCD_RD_DATA) refers to timer.o(i.delay_us) for delay_us
    lcd.o(i.LCD_RD_DATA) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_ReadPoint) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_ReadPoint) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_ReadPoint) refers to lcd.o(i.opt_delay) for opt_delay
    lcd.o(i.LCD_ReadPoint) refers to lcd.o(i.LCD_BGR2RGB) for LCD_BGR2RGB
    lcd.o(i.LCD_ReadPoint) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_ReadReg) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_ReadReg) refers to lcd.o(i.LCD_RD_DATA) for LCD_RD_DATA
    lcd.o(i.LCD_SSD_BackLightSet) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_SSD_BackLightSet) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    lcd.o(i.LCD_SSD_BackLightSet) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    lcd.o(i.LCD_SSD_BackLightSet) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    lcd.o(i.LCD_Scan_Dir) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_Scan_Dir) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_Scan_Dir) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_SetCursor) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_SetCursor) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_SetCursor) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_Set_Window) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_Set_Window) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_Set_Window) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_Set_Window) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_ShowChar) refers to lcd.o(i.LCD_Fast_DrawPoint) for LCD_Fast_DrawPoint
    lcd.o(i.LCD_ShowChar) refers to lcd.o(.constdata) for asc2_1206
    lcd.o(i.LCD_ShowChar) refers to lcd.o(.data) for POINT_COLOR
    lcd.o(i.LCD_ShowChar) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_ShowNum) refers to lcd.o(i.LCD_Pow) for LCD_Pow
    lcd.o(i.LCD_ShowNum) refers to lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    lcd.o(i.LCD_ShowString) refers to lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    lcd.o(i.LCD_ShowxNum) refers to lcd.o(i.LCD_Pow) for LCD_Pow
    lcd.o(i.LCD_ShowxNum) refers to lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    lcd.o(i.LCD_WriteRAM_Prepare) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_WriteRAM_Prepare) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_WriteReg) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    stm32f10x_it.o(i.TIM2_IRQHandler) refers to _printf_pad.o(.text) for _printf_pre_padding
    stm32f10x_it.o(i.TIM2_IRQHandler) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    stm32f10x_it.o(i.TIM2_IRQHandler) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    stm32f10x_it.o(i.TIM2_IRQHandler) refers to _printf_dec.o(.text) for _printf_int_dec
    stm32f10x_it.o(i.TIM2_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    stm32f10x_it.o(i.TIM2_IRQHandler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    stm32f10x_it.o(i.TIM2_IRQHandler) refers to rtc.o(i.IsLeapYear) for IsLeapYear
    stm32f10x_it.o(i.TIM2_IRQHandler) refers to noretval__2printf.o(.text) for __2printf
    stm32f10x_it.o(i.TIM2_IRQHandler) refers to rtc.o(i.Alarm_Check) for Alarm_Check
    stm32f10x_it.o(i.TIM2_IRQHandler) refers to led.o(i.LED_StartBlink) for LED_StartBlink
    stm32f10x_it.o(i.TIM2_IRQHandler) refers to rtc.o(.data) for g_TimerCounter
    stm32f10x_it.o(i.TIM2_IRQHandler) refers to rtc.o(.bss) for g_DateTime
    stm32f10x_it.o(i.TIM2_IRQHandler) refers to rtc.o(.constdata) for g_MonthDays
    stm32f10x_it.o(i.TIM3_IRQHandler) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    stm32f10x_it.o(i.TIM3_IRQHandler) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    stm32f10x_it.o(i.TIM3_IRQHandler) refers to _printf_dec.o(.text) for _printf_int_dec
    stm32f10x_it.o(i.TIM3_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    stm32f10x_it.o(i.TIM3_IRQHandler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    stm32f10x_it.o(i.TIM3_IRQHandler) refers to led.o(i.LED1_Toggle) for LED1_Toggle
    stm32f10x_it.o(i.TIM3_IRQHandler) refers to led.o(i.LED2_Toggle) for LED2_Toggle
    stm32f10x_it.o(i.TIM3_IRQHandler) refers to noretval__2printf.o(.text) for __2printf
    stm32f10x_it.o(i.TIM3_IRQHandler) refers to led.o(i.LED_StopBlink) for LED_StopBlink
    stm32f10x_it.o(i.TIM3_IRQHandler) refers to stm32f10x_it.o(.data) for blink_count
    main.o(i.main) refers to _printf_pad.o(.text) for _printf_pre_padding
    main.o(i.main) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.main) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main.o(i.main) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(i.main) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    main.o(i.main) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    main.o(i.main) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    main.o(i.main) refers to _printf_str.o(.text) for _printf_str
    main.o(i.main) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    main.o(i.main) refers to led.o(i.LED_Init) for LED_Init
    main.o(i.main) refers to key.o(i.KEY_Init) for KEY_Init
    main.o(i.main) refers to usart.o(i.uart1_init) for uart1_init
    main.o(i.main) refers to rtc.o(i.RTC_Init) for RTC_Init
    main.o(i.main) refers to timer.o(i.TIM2_Init) for TIM2_Init
    main.o(i.main) refers to timer.o(i.TIM3_Init) for TIM3_Init
    main.o(i.main) refers to timer.o(i.TIM4_Init) for TIM4_Init
    main.o(i.main) refers to uart_protocol.o(i.UART_Protocol_Init) for UART_Protocol_Init
    main.o(i.main) refers to clock_display.o(i.Clock_Init) for Clock_Init
    main.o(i.main) refers to rt_memclr.o(.text) for __aeabi_memclr
    main.o(i.main) refers to strcpy.o(.text) for strcpy
    main.o(i.main) refers to rtc.o(i.Alarm_Add) for Alarm_Add
    main.o(i.main) refers to noretval__2printf.o(.text) for __2printf
    main.o(i.main) refers to key.o(i.KEY_Scan) for KEY_Scan
    main.o(i.main) refers to clock_display.o(i.Clock_ProcessKey) for Clock_ProcessKey
    main.o(i.main) refers to clock_display.o(i.Clock_Display) for Clock_Display
    main.o(i.main) refers to uart_protocol.o(i.UART_Protocol_Process) for UART_Protocol_Process
    main.o(i.main) refers to timer.o(i.delay_ms) for delay_ms
    main.o(i.main) refers to rtc.o(.bss) for g_AlarmManager
    usart.o(i.USART1_IRQHandler) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart.o(i.USART1_IRQHandler) refers to usart.o(.data) for USART_RX_STA
    usart.o(i.USART1_IRQHandler) refers to usart.o(.bss) for USART_RX_BUF
    usart.o(i.USART_SendString) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.USART_SendString) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    usart.o(i.USART_SendString) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(i._sys_exit) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.fputc) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.fputc) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(i.uart1_init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart1_init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.uart1_init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.uart1_init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart.o(i.uart1_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart.o(i.uart1_init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart.o(i.uart1_init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(.bss) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    clock_display.o(i.Clock_ChangePage) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    clock_display.o(i.Clock_ChangePage) refers to clock_display.o(.data) for g_CurrentPage
    clock_display.o(i.Clock_Display) refers to clock_display.o(i.Clock_DrawDigitalTime) for Clock_DrawDigitalTime
    clock_display.o(i.Clock_Display) refers to clock_display.o(i.Clock_DrawAlarmList) for Clock_DrawAlarmList
    clock_display.o(i.Clock_Display) refers to clock_display.o(i.Clock_DrawAlarmEdit) for Clock_DrawAlarmEdit
    clock_display.o(i.Clock_Display) refers to clock_display.o(i.Clock_DrawTimeSet) for Clock_DrawTimeSet
    clock_display.o(i.Clock_Display) refers to clock_display.o(.data) for g_CurrentPage
    clock_display.o(i.Clock_DrawAlarmEdit) refers to _printf_pad.o(.text) for _printf_pre_padding
    clock_display.o(i.Clock_DrawAlarmEdit) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    clock_display.o(i.Clock_DrawAlarmEdit) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    clock_display.o(i.Clock_DrawAlarmEdit) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    clock_display.o(i.Clock_DrawAlarmEdit) refers to _printf_dec.o(.text) for _printf_int_dec
    clock_display.o(i.Clock_DrawAlarmEdit) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    clock_display.o(i.Clock_DrawAlarmEdit) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    clock_display.o(i.Clock_DrawAlarmEdit) refers to lcd.o(i.LCD_ShowString) for LCD_ShowString
    clock_display.o(i.Clock_DrawAlarmEdit) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    clock_display.o(i.Clock_DrawAlarmEdit) refers to noretval__2printf.o(.text) for __2printf
    clock_display.o(i.Clock_DrawAlarmEdit) refers to rt_memclr.o(.text) for __aeabi_memclr
    clock_display.o(i.Clock_DrawAlarmEdit) refers to noretval__2sprintf.o(.text) for __2sprintf
    clock_display.o(i.Clock_DrawAlarmEdit) refers to lcd.o(i.LCD_Fill) for LCD_Fill
    clock_display.o(i.Clock_DrawAlarmEdit) refers to lcd.o(i.LCD_DrawRectangle) for LCD_DrawRectangle
    clock_display.o(i.Clock_DrawAlarmEdit) refers to strcat.o(.text) for strcat
    clock_display.o(i.Clock_DrawAlarmEdit) refers to lcd.o(.data) for BACK_COLOR
    clock_display.o(i.Clock_DrawAlarmEdit) refers to rtc.o(.bss) for g_AlarmManager
    clock_display.o(i.Clock_DrawAlarmEdit) refers to clock_display.o(.data) for g_FirstTimeEnter
    clock_display.o(i.Clock_DrawAlarmEdit) refers to clock_display.o(.bss) for g_TempAlarm
    clock_display.o(i.Clock_DrawAlarmList) refers to _printf_pad.o(.text) for _printf_pre_padding
    clock_display.o(i.Clock_DrawAlarmList) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    clock_display.o(i.Clock_DrawAlarmList) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    clock_display.o(i.Clock_DrawAlarmList) refers to _printf_dec.o(.text) for _printf_int_dec
    clock_display.o(i.Clock_DrawAlarmList) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    clock_display.o(i.Clock_DrawAlarmList) refers to lcd.o(i.LCD_ShowString) for LCD_ShowString
    clock_display.o(i.Clock_DrawAlarmList) refers to lcd.o(i.LCD_Fill) for LCD_Fill
    clock_display.o(i.Clock_DrawAlarmList) refers to lcd.o(i.LCD_DrawRectangle) for LCD_DrawRectangle
    clock_display.o(i.Clock_DrawAlarmList) refers to noretval__2sprintf.o(.text) for __2sprintf
    clock_display.o(i.Clock_DrawAlarmList) refers to strcat.o(.text) for strcat
    clock_display.o(i.Clock_DrawAlarmList) refers to lcd.o(.data) for BACK_COLOR
    clock_display.o(i.Clock_DrawAlarmList) refers to rtc.o(.bss) for g_AlarmManager
    clock_display.o(i.Clock_DrawAlarmList) refers to clock_display.o(.data) for g_SelectedItem
    clock_display.o(i.Clock_DrawDate) refers to clock_display.o(i.Clock_DrawDigitalTime) for Clock_DrawDigitalTime
    clock_display.o(i.Clock_DrawDial) refers to lcd.o(i.LCD_Draw_Circle) for LCD_Draw_Circle
    clock_display.o(i.Clock_DrawDial) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    clock_display.o(i.Clock_DrawDial) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    clock_display.o(i.Clock_DrawDial) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    clock_display.o(i.Clock_DrawDial) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    clock_display.o(i.Clock_DrawDial) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    clock_display.o(i.Clock_DrawDial) refers to sin.o(i.sin) for sin
    clock_display.o(i.Clock_DrawDial) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    clock_display.o(i.Clock_DrawDial) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    clock_display.o(i.Clock_DrawDial) refers to cos.o(i.cos) for cos
    clock_display.o(i.Clock_DrawDial) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    clock_display.o(i.Clock_DrawDial) refers to clock_display.o(i.LCD_DrawPoint_big) for LCD_DrawPoint_big
    clock_display.o(i.Clock_DrawDial) refers to lcd.o(.data) for POINT_COLOR
    clock_display.o(i.Clock_DrawDigitalTime) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    clock_display.o(i.Clock_DrawDigitalTime) refers to lcd.o(i.LCD_ShowString) for LCD_ShowString
    clock_display.o(i.Clock_DrawDigitalTime) refers to lcd.o(i.LCD_Fill) for LCD_Fill
    clock_display.o(i.Clock_DrawDigitalTime) refers to lcd.o(i.LCD_DrawRectangle) for LCD_DrawRectangle
    clock_display.o(i.Clock_DrawDigitalTime) refers to clock_display.o(i.Clock_RefreshTimeDigit) for Clock_RefreshTimeDigit
    clock_display.o(i.Clock_DrawDigitalTime) refers to clock_display.o(.data) for g_NeedRefresh
    clock_display.o(i.Clock_DrawDigitalTime) refers to lcd.o(.data) for POINT_COLOR
    clock_display.o(i.Clock_DrawHands) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    clock_display.o(i.Clock_DrawHands) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    clock_display.o(i.Clock_DrawHands) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    clock_display.o(i.Clock_DrawHands) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    clock_display.o(i.Clock_DrawHands) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    clock_display.o(i.Clock_DrawHands) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    clock_display.o(i.Clock_DrawHands) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    clock_display.o(i.Clock_DrawHands) refers to sin.o(i.sin) for sin
    clock_display.o(i.Clock_DrawHands) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    clock_display.o(i.Clock_DrawHands) refers to cos.o(i.cos) for cos
    clock_display.o(i.Clock_DrawHands) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    clock_display.o(i.Clock_DrawHands) refers to lcd.o(i.LCD_DrawLine) for LCD_DrawLine
    clock_display.o(i.Clock_DrawHands) refers to lcd.o(i.LCD_Draw_Circle) for LCD_Draw_Circle
    clock_display.o(i.Clock_DrawHands) refers to lcd.o(.data) for POINT_COLOR
    clock_display.o(i.Clock_DrawTimeSet) refers to _printf_pad.o(.text) for _printf_pre_padding
    clock_display.o(i.Clock_DrawTimeSet) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    clock_display.o(i.Clock_DrawTimeSet) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    clock_display.o(i.Clock_DrawTimeSet) refers to _printf_dec.o(.text) for _printf_int_dec
    clock_display.o(i.Clock_DrawTimeSet) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    clock_display.o(i.Clock_DrawTimeSet) refers to _printf_str.o(.text) for _printf_str
    clock_display.o(i.Clock_DrawTimeSet) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    clock_display.o(i.Clock_DrawTimeSet) refers to lcd.o(i.LCD_ShowString) for LCD_ShowString
    clock_display.o(i.Clock_DrawTimeSet) refers to noretval__2printf.o(.text) for __2printf
    clock_display.o(i.Clock_DrawTimeSet) refers to noretval__2sprintf.o(.text) for __2sprintf
    clock_display.o(i.Clock_DrawTimeSet) refers to lcd.o(i.LCD_Fill) for LCD_Fill
    clock_display.o(i.Clock_DrawTimeSet) refers to lcd.o(i.LCD_DrawRectangle) for LCD_DrawRectangle
    clock_display.o(i.Clock_DrawTimeSet) refers to lcd.o(.data) for BACK_COLOR
    clock_display.o(i.Clock_DrawTimeSet) refers to clock_display.o(.data) for g_FirstTimeEnter
    clock_display.o(i.Clock_DrawTimeSet) refers to clock_display.o(.bss) for g_TempDateTime
    clock_display.o(i.Clock_Init) refers to lcd.o(i.LCD_Init) for LCD_Init
    clock_display.o(i.Clock_Init) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    clock_display.o(i.Clock_Init) refers to clock_display.o(.data) for g_CurrentPage
    clock_display.o(i.Clock_ProcessKey) refers to _printf_pad.o(.text) for _printf_pre_padding
    clock_display.o(i.Clock_ProcessKey) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    clock_display.o(i.Clock_ProcessKey) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    clock_display.o(i.Clock_ProcessKey) refers to _printf_dec.o(.text) for _printf_int_dec
    clock_display.o(i.Clock_ProcessKey) refers to clock_display.o(i.Clock_ChangePage) for Clock_ChangePage
    clock_display.o(i.Clock_ProcessKey) refers to clock_display.o(i.SelectCallback_SetTime) for SelectCallback_SetTime
    clock_display.o(i.Clock_ProcessKey) refers to clock_display.o(i.SelectCallback_GotoClock) for SelectCallback_GotoClock
    clock_display.o(i.Clock_ProcessKey) refers to clock_display.o(i.SelectCallback_AddAlarm) for SelectCallback_AddAlarm
    clock_display.o(i.Clock_ProcessKey) refers to clock_display.o(i.SelectCallback_EditAlarm) for SelectCallback_EditAlarm
    clock_display.o(i.Clock_ProcessKey) refers to clock_display.o(i.SelectCallback_CancelEdit) for SelectCallback_CancelEdit
    clock_display.o(i.Clock_ProcessKey) refers to clock_display.o(i.SelectCallback_SaveAlarm) for SelectCallback_SaveAlarm
    clock_display.o(i.Clock_ProcessKey) refers to clock_display.o(i.GetDayOfWeek) for GetDayOfWeek
    clock_display.o(i.Clock_ProcessKey) refers to clock_display.o(i.SelectCallback_SaveTime) for SelectCallback_SaveTime
    clock_display.o(i.Clock_ProcessKey) refers to clock_display.o(i.SelectCallback_CancelTimeSet) for SelectCallback_CancelTimeSet
    clock_display.o(i.Clock_ProcessKey) refers to noretval__2printf.o(.text) for __2printf
    clock_display.o(i.Clock_ProcessKey) refers to rtc.o(i.IsLeapYear) for IsLeapYear
    clock_display.o(i.Clock_ProcessKey) refers to clock_display.o(.data) for g_CurrentPage
    clock_display.o(i.Clock_ProcessKey) refers to rtc.o(.bss) for g_AlarmManager
    clock_display.o(i.Clock_ProcessKey) refers to clock_display.o(.bss) for g_TempDateTime
    clock_display.o(i.Clock_ProcessKey) refers to rtc.o(.constdata) for g_MonthDays
    clock_display.o(i.Clock_ProcessKey) refers to strcmpv7m.o(.text) for strcmp
    clock_display.o(i.Clock_ProcessKey) refers to strcpy.o(.text) for strcpy
    clock_display.o(i.Clock_RefreshTimeDigit) refers to _printf_pad.o(.text) for _printf_pre_padding
    clock_display.o(i.Clock_RefreshTimeDigit) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    clock_display.o(i.Clock_RefreshTimeDigit) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    clock_display.o(i.Clock_RefreshTimeDigit) refers to _printf_dec.o(.text) for _printf_int_dec
    clock_display.o(i.Clock_RefreshTimeDigit) refers to noretval__2sprintf.o(.text) for __2sprintf
    clock_display.o(i.Clock_RefreshTimeDigit) refers to lcd.o(i.LCD_Fill) for LCD_Fill
    clock_display.o(i.Clock_RefreshTimeDigit) refers to lcd.o(i.LCD_ShowString) for LCD_ShowString
    clock_display.o(i.Clock_RefreshTimeDigit) refers to lcd.o(.data) for POINT_COLOR
    clock_display.o(i.Clock_RefreshTimeDigit) refers to clock_display.o(.data) for g_LastSecond
    clock_display.o(i.LCD_DrawPoint_big) refers to lcd.o(i.LCD_Fill) for LCD_Fill
    clock_display.o(i.LCD_DrawPoint_big) refers to lcd.o(.data) for POINT_COLOR
    clock_display.o(i.SelectCallback_AddAlarm) refers to clock_display.o(i.Clock_ChangePage) for Clock_ChangePage
    clock_display.o(i.SelectCallback_AddAlarm) refers to rtc.o(.bss) for g_AlarmManager
    clock_display.o(i.SelectCallback_AddAlarm) refers to clock_display.o(.data) for g_CurrentAlarmIndex
    clock_display.o(i.SelectCallback_CancelEdit) refers to clock_display.o(i.Clock_ChangePage) for Clock_ChangePage
    clock_display.o(i.SelectCallback_CancelEdit) refers to clock_display.o(.data) for g_EditingField
    clock_display.o(i.SelectCallback_CancelTimeSet) refers to clock_display.o(i.Clock_ChangePage) for Clock_ChangePage
    clock_display.o(i.SelectCallback_CancelTimeSet) refers to clock_display.o(.data) for g_TimeEditMode
    clock_display.o(i.SelectCallback_DeleteAlarm) refers to rtc.o(i.Alarm_Delete) for Alarm_Delete
    clock_display.o(i.SelectCallback_DeleteAlarm) refers to clock_display.o(i.Clock_ChangePage) for Clock_ChangePage
    clock_display.o(i.SelectCallback_DeleteAlarm) refers to rtc.o(.bss) for g_AlarmManager
    clock_display.o(i.SelectCallback_EditAlarm) refers to clock_display.o(i.Clock_ChangePage) for Clock_ChangePage
    clock_display.o(i.SelectCallback_EditAlarm) refers to clock_display.o(.data) for g_CurrentAlarmIndex
    clock_display.o(i.SelectCallback_GotoAlarmList) refers to clock_display.o(i.Clock_ChangePage) for Clock_ChangePage
    clock_display.o(i.SelectCallback_GotoClock) refers to clock_display.o(i.Clock_ChangePage) for Clock_ChangePage
    clock_display.o(i.SelectCallback_SaveAlarm) refers to _printf_pad.o(.text) for _printf_pre_padding
    clock_display.o(i.SelectCallback_SaveAlarm) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    clock_display.o(i.SelectCallback_SaveAlarm) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    clock_display.o(i.SelectCallback_SaveAlarm) refers to _printf_dec.o(.text) for _printf_int_dec
    clock_display.o(i.SelectCallback_SaveAlarm) refers to noretval__2printf.o(.text) for __2printf
    clock_display.o(i.SelectCallback_SaveAlarm) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    clock_display.o(i.SelectCallback_SaveAlarm) refers to clock_display.o(i.Clock_ChangePage) for Clock_ChangePage
    clock_display.o(i.SelectCallback_SaveAlarm) refers to clock_display.o(.bss) for g_TempAlarm
    clock_display.o(i.SelectCallback_SaveAlarm) refers to rtc.o(.bss) for g_AlarmManager
    clock_display.o(i.SelectCallback_SaveAlarm) refers to clock_display.o(.data) for g_CurrentAlarmIndex
    clock_display.o(i.SelectCallback_SaveTime) refers to _printf_pad.o(.text) for _printf_pre_padding
    clock_display.o(i.SelectCallback_SaveTime) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    clock_display.o(i.SelectCallback_SaveTime) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    clock_display.o(i.SelectCallback_SaveTime) refers to _printf_dec.o(.text) for _printf_int_dec
    clock_display.o(i.SelectCallback_SaveTime) refers to noretval__2printf.o(.text) for __2printf
    clock_display.o(i.SelectCallback_SaveTime) refers to rtc.o(i.RTC_SetTime) for RTC_SetTime
    clock_display.o(i.SelectCallback_SaveTime) refers to clock_display.o(i.Clock_ChangePage) for Clock_ChangePage
    clock_display.o(i.SelectCallback_SaveTime) refers to clock_display.o(.bss) for g_TempDateTime
    clock_display.o(i.SelectCallback_SaveTime) refers to clock_display.o(.data) for g_TimeEditMode
    clock_display.o(i.SelectCallback_SetTime) refers to clock_display.o(i.Clock_ChangePage) for Clock_ChangePage
    clock_display.o(i.SelectCallback_SetTime) refers to clock_display.o(.data) for g_FirstTimeEnter
    clock_display.o(.data) refers to clock_display.o(.conststring) for .conststring
    led.o(i.LED_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    led.o(i.LED_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    led.o(i.LED_Init) refers to led.o(i.LED1_Off) for LED1_Off
    led.o(i.LED_Init) refers to led.o(i.LED2_Off) for LED2_Off
    led.o(i.LED_StartBlink) refers to noretval__2printf.o(.text) for __2printf
    led.o(i.LED_StartBlink) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    led.o(i.LED_StartBlink) refers to stm32f10x_tim.o(i.TIM_SetCounter) for TIM_SetCounter
    led.o(i.LED_StartBlink) refers to led.o(i.LED1_On) for LED1_On
    led.o(i.LED_StartBlink) refers to led.o(i.LED2_On) for LED2_On
    led.o(i.LED_StopBlink) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    led.o(i.LED_StopBlink) refers to led.o(i.LED1_Off) for LED1_Off
    led.o(i.LED_StopBlink) refers to led.o(i.LED2_Off) for LED2_Off
    led.o(i.LED_Toggle) refers to led.o(i.LED1_Toggle) for LED1_Toggle
    led.o(i.LED_Toggle) refers to led.o(i.LED2_Toggle) for LED2_Toggle
    rtc.o(i.Alarm_Add) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    rtc.o(i.Alarm_Check) refers to _printf_pad.o(.text) for _printf_pre_padding
    rtc.o(i.Alarm_Check) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    rtc.o(i.Alarm_Check) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    rtc.o(i.Alarm_Check) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    rtc.o(i.Alarm_Check) refers to _printf_dec.o(.text) for _printf_int_dec
    rtc.o(i.Alarm_Check) refers to _printf_str.o(.text) for _printf_str
    rtc.o(i.Alarm_Check) refers to noretval__2sprintf.o(.text) for __2sprintf
    rtc.o(i.Alarm_Check) refers to usart.o(i.USART_SendString) for USART_SendString
    rtc.o(i.Alarm_Check) refers to led.o(i.LED_StartBlink) for LED_StartBlink
    rtc.o(i.Alarm_Delete) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    rtc.o(i.Alarm_Init) refers to rt_memclr.o(.text) for __aeabi_memclr
    rtc.o(i.Alarm_Update) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    rtc.o(i.DateTime_Update) refers to rtc.o(i.IsLeapYear) for IsLeapYear
    rtc.o(i.DateTime_Update) refers to rtc.o(.constdata) for g_MonthDays
    rtc.o(i.RTC_GetTime) refers to rtc.o(.bss) for g_DateTime
    rtc.o(i.RTC_Init) refers to rtc.o(i.Alarm_Init) for Alarm_Init
    rtc.o(i.RTC_Init) refers to rtc.o(.bss) for g_DateTime
    rtc.o(i.RTC_SetTime) refers to rtc.o(.bss) for g_DateTime
    timer.o(i.TIM2_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    timer.o(i.TIM2_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    timer.o(i.TIM2_Init) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    timer.o(i.TIM2_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    timer.o(i.TIM2_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    timer.o(i.TIM3_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    timer.o(i.TIM3_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    timer.o(i.TIM3_Init) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    timer.o(i.TIM3_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    timer.o(i.TIM3_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    timer.o(i.TIM4_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    timer.o(i.TIM4_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    timer.o(i.TIM4_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    timer.o(i.delay_ms) refers to timer.o(i.delay_us) for delay_us
    timer.o(i.delay_us) refers to stm32f10x_tim.o(i.TIM_SetCounter) for TIM_SetCounter
    timer.o(i.delay_us) refers to stm32f10x_tim.o(i.TIM_GetCounter) for TIM_GetCounter
    key.o(i.KEY_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    key.o(i.KEY_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    key.o(i.KEY_Scan) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    key.o(i.KEY_Scan) refers to timer.o(i.delay_ms) for delay_ms
    key.o(i.KEY_Scan) refers to key.o(.data) for key_up
    uart_protocol.o(i.UART_Parse_Alarm) refers to _scanf_int.o(.text) for _scanf_int
    uart_protocol.o(i.UART_Parse_Alarm) refers to _scanf_str.o(.text) for _scanf_string
    uart_protocol.o(i.UART_Parse_Alarm) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    uart_protocol.o(i.UART_Parse_Alarm) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    uart_protocol.o(i.UART_Parse_Alarm) refers to _printf_dec.o(.text) for _printf_int_dec
    uart_protocol.o(i.UART_Parse_Alarm) refers to strchr.o(.text) for strchr
    uart_protocol.o(i.UART_Parse_Alarm) refers to noretval__2printf.o(.text) for __2printf
    uart_protocol.o(i.UART_Parse_Alarm) refers to __0sscanf.o(.text) for __0sscanf
    uart_protocol.o(i.UART_Parse_Alarm) refers to strncpy.o(.text) for strncpy
    uart_protocol.o(i.UART_Parse_Alarm) refers to strlen.o(.text) for strlen
    uart_protocol.o(i.UART_Parse_Alarm) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    uart_protocol.o(i.UART_Parse_Alarm) refers to rtc.o(.bss) for g_AlarmManager
    uart_protocol.o(i.UART_Parse_Command) refers to strncmp.o(.text) for strncmp
    uart_protocol.o(i.UART_Parse_Time) refers to _scanf_int.o(.text) for _scanf_int
    uart_protocol.o(i.UART_Parse_Time) refers to _printf_pad.o(.text) for _printf_pre_padding
    uart_protocol.o(i.UART_Parse_Time) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    uart_protocol.o(i.UART_Parse_Time) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    uart_protocol.o(i.UART_Parse_Time) refers to _printf_dec.o(.text) for _printf_int_dec
    uart_protocol.o(i.UART_Parse_Time) refers to strchr.o(.text) for strchr
    uart_protocol.o(i.UART_Parse_Time) refers to noretval__2printf.o(.text) for __2printf
    uart_protocol.o(i.UART_Parse_Time) refers to __0sscanf.o(.text) for __0sscanf
    uart_protocol.o(i.UART_Parse_Time) refers to clock_display.o(i.GetDayOfWeek) for GetDayOfWeek
    uart_protocol.o(i.UART_Parse_Time) refers to rtc.o(i.RTC_SetTime) for RTC_SetTime
    uart_protocol.o(i.UART_Protocol_Init) refers to noretval__2printf.o(.text) for __2printf
    uart_protocol.o(i.UART_Protocol_Process) refers to uart_protocol.o(i.UART_Parse_Command) for UART_Parse_Command
    uart_protocol.o(i.UART_Protocol_Process) refers to uart_protocol.o(i.UART_Send_Time) for UART_Send_Time
    uart_protocol.o(i.UART_Protocol_Process) refers to uart_protocol.o(i.UART_Parse_Time) for UART_Parse_Time
    uart_protocol.o(i.UART_Protocol_Process) refers to atoi.o(.text) for atoi
    uart_protocol.o(i.UART_Protocol_Process) refers to uart_protocol.o(i.UART_Send_Alarm) for UART_Send_Alarm
    uart_protocol.o(i.UART_Protocol_Process) refers to noretval__2printf.o(.text) for __2printf
    uart_protocol.o(i.UART_Protocol_Process) refers to uart_protocol.o(i.UART_Parse_Alarm) for UART_Parse_Alarm
    uart_protocol.o(i.UART_Protocol_Process) refers to uart_protocol.o(i.UART_Send_Help) for UART_Send_Help
    uart_protocol.o(i.UART_Protocol_Process) refers to usart.o(.data) for USART_RX_STA
    uart_protocol.o(i.UART_Protocol_Process) refers to usart.o(.bss) for USART_RX_BUF
    uart_protocol.o(i.UART_Protocol_Process) refers to rtc.o(.bss) for g_AlarmManager
    uart_protocol.o(i.UART_Send_Alarm) refers to _printf_pad.o(.text) for _printf_pre_padding
    uart_protocol.o(i.UART_Send_Alarm) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    uart_protocol.o(i.UART_Send_Alarm) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    uart_protocol.o(i.UART_Send_Alarm) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    uart_protocol.o(i.UART_Send_Alarm) refers to _printf_dec.o(.text) for _printf_int_dec
    uart_protocol.o(i.UART_Send_Alarm) refers to _printf_str.o(.text) for _printf_str
    uart_protocol.o(i.UART_Send_Alarm) refers to noretval__2printf.o(.text) for __2printf
    uart_protocol.o(i.UART_Send_Alarm) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    uart_protocol.o(i.UART_Send_Alarm) refers to strcpy.o(.text) for strcpy
    uart_protocol.o(i.UART_Send_Alarm) refers to strcat.o(.text) for strcat
    uart_protocol.o(i.UART_Send_Alarm) refers to strlen.o(.text) for strlen
    uart_protocol.o(i.UART_Send_Alarm) refers to rtc.o(.bss) for g_AlarmManager
    uart_protocol.o(i.UART_Send_Help) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    uart_protocol.o(i.UART_Send_Help) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    uart_protocol.o(i.UART_Send_Help) refers to _printf_dec.o(.text) for _printf_int_dec
    uart_protocol.o(i.UART_Send_Help) refers to noretval__2printf.o(.text) for __2printf
    uart_protocol.o(i.UART_Send_Time) refers to _printf_pad.o(.text) for _printf_pre_padding
    uart_protocol.o(i.UART_Send_Time) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    uart_protocol.o(i.UART_Send_Time) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    uart_protocol.o(i.UART_Send_Time) refers to _printf_dec.o(.text) for _printf_int_dec
    uart_protocol.o(i.UART_Send_Time) refers to noretval__2printf.o(.text) for __2printf
    uart_protocol.o(i.UART_Send_Time) refers to rtc.o(.bss) for g_DateTime
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to usart.o(.data) for __stdout
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to usart.o(.data) for __stdout
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int.o(.text) for _printf_int_hex
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __0sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    __0sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    _scanf_int.o(.text) refers to _chval.o(.text) for _chval
    atoi.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    strncpy.o(.text) refers to rt_memclr.o(.text) for __aeabi_memclr
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    cos.o(i.__softfp_cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos.o(i.__softfp_cos) refers to cos.o(i.cos) for cos
    cos.o(i.cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos.o(i.cos) refers to _rserrno.o(.text) for __set_errno
    cos.o(i.cos) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    cos.o(i.cos) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    cos.o(i.cos) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    cos.o(i.cos) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    cos.o(i.cos) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    cos_x.o(i.____softfp_cos$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_x.o(i.____softfp_cos$lsc) refers to cos_x.o(i.__cos$lsc) for __cos$lsc
    cos_x.o(i.__cos$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_x.o(i.__cos$lsc) refers to _rserrno.o(.text) for __set_errno
    cos_x.o(i.__cos$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    cos_x.o(i.__cos$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    cos_x.o(i.__cos$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    cos_x.o(i.__cos$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin.o(i.__softfp_sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.__softfp_sin) refers to sin.o(i.sin) for sin
    sin.o(i.sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.sin) refers to _rserrno.o(.text) for __set_errno
    sin.o(i.sin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    sin.o(i.sin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin.o(i.sin) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin.o(i.sin) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin.o(i.sin) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    sin_x.o(i.____softfp_sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_x.o(i.____softfp_sin$lsc) refers to sin_x.o(i.__sin$lsc) for __sin$lsc
    sin_x.o(i.__sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_x.o(i.__sin$lsc) refers to _rserrno.o(.text) for __set_errno
    sin_x.o(i.__sin$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin_x.o(i.__sin$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin_x.o(i.__sin$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin_x.o(i.__sin$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_wp.o(.text) for __printf
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(i.fputc) for fputc
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace.o(.text) for isspace
    strtol.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    cos_i.o(i.__kernel_cos) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    cos_i.o(i.__kernel_cos) refers to poly.o(i.__kernel_poly) for __kernel_poly
    cos_i.o(i.__kernel_cos) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    cos_i.o(i.__kernel_cos) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    cos_i.o(i.__kernel_cos) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    cos_i.o(i.__kernel_cos) refers to cos_i.o(.constdata) for .constdata
    cos_i.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    rred.o(i.__ieee754_rem_pio2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    rred.o(i.__ieee754_rem_pio2) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    rred.o(i.__ieee754_rem_pio2) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    rred.o(i.__ieee754_rem_pio2) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    rred.o(i.__ieee754_rem_pio2) refers to rred.o(.constdata) for .constdata
    rred.o(i.__use_accurate_range_reduction) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    sin_i.o(i.__kernel_sin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    sin_i.o(i.__kernel_sin) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sin_i.o(i.__kernel_sin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i.o(i.__kernel_sin) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sin_i.o(i.__kernel_sin) refers to sin_i.o(.constdata) for .constdata
    sin_i.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sin_i_x.o(i.____kernel_sin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i_x.o(i.____kernel_sin$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sin_i_x.o(i.____kernel_sin$lsc) refers to sin_i_x.o(.constdata) for .constdata
    sin_i_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    isspace.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    _scanf.o(.text) refers (Weak) to _scanf_int.o(.text) for _scanf_int
    _scanf.o(.text) refers (Weak) to _scanf_str.o(.text) for _scanf_string
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_md.o(.text) for __user_initial_stackheap
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(i._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_exit.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing misc.o(i.NVIC_PriorityGroupConfig), (20 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (40 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearITPendingBit), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_Cmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DMACmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DeInit), (92 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeChannelCountConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetConversionValue), (8 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetFlagStatus), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetITStatus), (36 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetInjectedConversionValue), (28 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartConvStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ITConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_Init), (80 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedChannelConfig), (130 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedDiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_RegularChannelConfig), (184 bytes).
    Removing stm32f10x_adc.o(i.ADC_ResetCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_SetInjectedOffset), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_StartCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_StructInit), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_TempSensorVrefintCmd), (36 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearFlag), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_DeInit), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetFlagStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetITStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ITConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_RTCOutputConfig), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ReadBackupRegister), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_SetRTCCalibrationValue), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinCmd), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinLevelConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_WriteBackupRegister), (28 bytes).
    Removing stm32f10x_can.o(i.CAN_CancelTransmit), (48 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearFlag), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearITPendingBit), (168 bytes).
    Removing stm32f10x_can.o(i.CAN_DBGFreeze), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_DeInit), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_FIFORelease), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_FilterInit), (264 bytes).
    Removing stm32f10x_can.o(i.CAN_GetFlagStatus), (120 bytes).
    Removing stm32f10x_can.o(i.CAN_GetITStatus), (288 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLSBTransmitErrorCounter), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLastErrorCode), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetReceiveErrorCounter), (10 bytes).
    Removing stm32f10x_can.o(i.CAN_ITConfig), (18 bytes).
    Removing stm32f10x_can.o(i.CAN_Init), (276 bytes).
    Removing stm32f10x_can.o(i.CAN_MessagePending), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_OperatingModeRequest), (162 bytes).
    Removing stm32f10x_can.o(i.CAN_Receive), (240 bytes).
    Removing stm32f10x_can.o(i.CAN_SlaveStartBank), (52 bytes).
    Removing stm32f10x_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f10x_can.o(i.CAN_TTComModeCmd), (118 bytes).
    Removing stm32f10x_can.o(i.CAN_Transmit), (294 bytes).
    Removing stm32f10x_can.o(i.CAN_TransmitStatus), (160 bytes).
    Removing stm32f10x_can.o(i.CAN_WakeUp), (48 bytes).
    Removing stm32f10x_can.o(i.CheckITStatus), (18 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearFlag), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearITPendingBit), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_Cmd), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_DeInit), (22 bytes).
    Removing stm32f10x_cec.o(i.CEC_EndOfMessageCmd), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetFlagStatus), (48 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetITStatus), (40 bytes).
    Removing stm32f10x_cec.o(i.CEC_ITConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_Init), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_OwnAddressConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_ReceiveDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SendDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SetPrescaler), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_StartOfMessage), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcBlockCRC), (36 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcCRC), (16 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetCRC), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetIDRegister), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_ResetDR), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_SetIDRegister), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_Cmd), (40 bytes).
    Removing stm32f10x_dac.o(i.DAC_DMACmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_DeInit), (22 bytes).
    Removing stm32f10x_dac.o(i.DAC_DualSoftwareTriggerCmd), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_GetDataOutputValue), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_Init), (52 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel1Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel2Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetDualChannelData), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_SoftwareTriggerCmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_WaveGenerationCmd), (40 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_Config), (32 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetREVID), (12 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearFlag), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearITPendingBit), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_Cmd), (24 bytes).
    Removing stm32f10x_dma.o(i.DMA_DeInit), (332 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetCurrDataCounter), (8 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetFlagStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetITStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_ITConfig), (18 bytes).
    Removing stm32f10x_dma.o(i.DMA_Init), (60 bytes).
    Removing stm32f10x_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f10x_dma.o(i.DMA_StructInit), (26 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetFlagStatus), (24 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetITStatus), (40 bytes).
    Removing stm32f10x_exti.o(i.EXTI_Init), (148 bytes).
    Removing stm32f10x_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ClearFlag), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EnableWriteProtection), (196 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllPages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseOptionBytes), (168 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ErasePage), (76 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetBank1Status), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetFlagStatus), (48 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetPrefetchBufferStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetStatus), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetUserOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetWriteProtectionOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_HalfCycleAccessCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ITConfig), (32 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Lock), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_LockBank1), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramHalfWord), (64 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramOptionByteData), (84 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramWord), (108 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ReadOutProtection), (172 bytes).
    Removing stm32f10x_flash.o(i.FLASH_SetLatency), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Unlock), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UnlockBank1), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UserOptionByteConfig), (104 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation), (38 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastOperation), (38 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearFlag), (64 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearITPendingBit), (72 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetECC), (28 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetFlagStatus), (56 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetITStatus), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ITConfig), (128 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDDeInit), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDECCCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDInit), (136 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd), (52 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMDeInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMInit), (230 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMStructInit), (114 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDCmd), (48 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDDeInit), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDInit), (132 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (200 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EXTILineConfig), (64 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ResetBits), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_WriteBit), (10 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ARPCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_AcknowledgeConfig), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CalculatePEC), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CheckEvent), (42 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearFlag), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Cmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMACmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMALastTransferCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DeInit), (56 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DualAddressCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_FastModeDutyCycleConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GeneralCallCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTART), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTOP), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetFlagStatus), (58 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetITStatus), (38 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetLastEvent), (26 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetPEC), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ITConfig), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Init), (236 bytes).
    Removing stm32f10x_i2c.o(i.I2C_NACKPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_OwnAddress2Config), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_PECPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReadRegister), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReceiveData), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SMBusAlertConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Send7bitAddress), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SendData), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SoftwareResetCmd), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StretchClockCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StructInit), (30 bytes).
    Removing stm32f10x_i2c.o(i.I2C_TransmitPEC), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_GetFlagStatus), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetReload), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_BackupAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_ClearFlag), (20 bytes).
    Removing stm32f10x_pwr.o(i.PWR_DeInit), (22 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTANDBYMode), (52 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTOPMode), (64 bytes).
    Removing stm32f10x_pwr.o(i.PWR_GetFlagStatus), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDLevelConfig), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_WakeUpPinCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (60 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearFlag), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_EnterConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ExitConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetCounter), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetDivider), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetFlagStatus), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetITStatus), (36 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ITConfig), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetAlarm), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetCounter), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetPrescaler), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForLastTask), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForSynchro), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CEATAITCmd), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearFlag), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClockCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CommandCompletionCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DMACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataConfig), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataStructInit), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DeInit), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetCommandResponse), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFlagStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetITStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetPowerState), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetResponse), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ITConfig), (32 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_Init), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ReadData), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCEATACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCommand), (44 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendSDIOSuspendCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetPowerState), (28 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOOperation), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOReadWaitMode), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StartSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StopSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StructInit), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_WriteData), (12 bytes).
    Removing stm32f10x_spi.o(i.I2S_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.I2S_Init), (232 bytes).
    Removing stm32f10x_spi.o(i.I2S_StructInit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_BiDirectionalLineConfig), (28 bytes).
    Removing stm32f10x_spi.o(i.SPI_CalculateCRC), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_DataSizeConfig), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRC), (16 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRCPolynomial), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearFlag), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DMACmd), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DeInit), (88 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetFlagStatus), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetITStatus), (52 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ITConfig), (32 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ReceiveData), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_SendData), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_Init), (60 bytes).
    Removing stm32f10x_spi.o(i.SPI_NSSInternalSoftwareConfig), (30 bytes).
    Removing stm32f10x_spi.o(i.SPI_SSOutputCmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_TransmitCRC), (10 bytes).
    Removing stm32f10x_tim.o(i.TI1_Config), (128 bytes).
    Removing stm32f10x_tim.o(i.TI2_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TI3_Config), (144 bytes).
    Removing stm32f10x_tim.o(i.TI4_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_ARRPreloadConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRConfig), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CtrlPWMOutputs), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (488 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (54 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (28 bytes).
    Removing stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig), (66 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICInit), (172 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_InternalClockConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1Init), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2Init), (164 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3Init), (160 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4Init), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (82 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOutputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC1Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC2Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC3Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC4Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (62 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearITPendingBit), (30 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_DeInit), (22 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_Enable), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_EnableIT), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_GetFlagStatus), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetCounter), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetPrescaler), (24 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetWindowValue), (40 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing system_stm32f10x.o(.data), (20 bytes).
    Removing lcd.o(i.LCD_BGR2RGB), (26 bytes).
    Removing lcd.o(i.LCD_Color_Fill), (124 bytes).
    Removing lcd.o(i.LCD_DisplayOff), (96 bytes).
    Removing lcd.o(i.LCD_DisplayOn), (96 bytes).
    Removing lcd.o(i.LCD_Draw_Circle), (152 bytes).
    Removing lcd.o(i.LCD_Pow), (22 bytes).
    Removing lcd.o(i.LCD_ReadPoint), (520 bytes).
    Removing lcd.o(i.LCD_Set_Window), (1078 bytes).
    Removing lcd.o(i.LCD_ShowNum), (148 bytes).
    Removing lcd.o(i.LCD_ShowxNum), (190 bytes).
    Removing lcd.o(i.LCD_WriteRAM), (36 bytes).
    Removing lcd.o(i.opt_delay), (14 bytes).
    Removing clock_display.o(i.Clock_DrawDate), (12 bytes).
    Removing clock_display.o(i.Clock_DrawDial), (196 bytes).
    Removing clock_display.o(i.Clock_DrawHands), (564 bytes).
    Removing clock_display.o(i.LCD_DrawPoint_big), (40 bytes).
    Removing clock_display.o(i.SelectCallback_DeleteAlarm), (28 bytes).
    Removing clock_display.o(i.SelectCallback_GotoAlarmList), (10 bytes).
    Removing led.o(i.LED_Toggle), (12 bytes).
    Removing rtc.o(i.Alarm_Delete), (78 bytes).
    Removing rtc.o(i.Alarm_Update), (42 bytes).
    Removing rtc.o(i.DateTime_Update), (156 bytes).
    Removing rtc.o(i.RTC_GetTime), (20 bytes).

476 unused section(s) (total 21522 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/ctype.c                          0x00000000   Number         0  isspace.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strncpy.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  atoi.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_str.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_int.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_char.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  __0sscanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _strtoul.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtol.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strchr.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strncmp.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcat.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/cos.c                         0x00000000   Number         0  cos.o ABSOLUTE
    ../mathlib/cos.c                         0x00000000   Number         0  cos_x.o ABSOLUTE
    ../mathlib/cos_i.c                       0x00000000   Number         0  cos_i.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/rred.c                        0x00000000   Number         0  rred.o ABSOLUTE
    ../mathlib/sin.c                         0x00000000   Number         0  sin_x.o ABSOLUTE
    ../mathlib/sin.c                         0x00000000   Number         0  sin.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i_x.o ABSOLUTE
    Hardware\lcd.c                           0x00000000   Number         0  lcd.o ABSOLUTE
    Library\misc.c                           0x00000000   Number         0  misc.o ABSOLUTE
    Library\stm32f10x_adc.c                  0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    Library\stm32f10x_bkp.c                  0x00000000   Number         0  stm32f10x_bkp.o ABSOLUTE
    Library\stm32f10x_can.c                  0x00000000   Number         0  stm32f10x_can.o ABSOLUTE
    Library\stm32f10x_cec.c                  0x00000000   Number         0  stm32f10x_cec.o ABSOLUTE
    Library\stm32f10x_crc.c                  0x00000000   Number         0  stm32f10x_crc.o ABSOLUTE
    Library\stm32f10x_dac.c                  0x00000000   Number         0  stm32f10x_dac.o ABSOLUTE
    Library\stm32f10x_dbgmcu.c               0x00000000   Number         0  stm32f10x_dbgmcu.o ABSOLUTE
    Library\stm32f10x_dma.c                  0x00000000   Number         0  stm32f10x_dma.o ABSOLUTE
    Library\stm32f10x_exti.c                 0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    Library\stm32f10x_flash.c                0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    Library\stm32f10x_fsmc.c                 0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    Library\stm32f10x_gpio.c                 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    Library\stm32f10x_i2c.c                  0x00000000   Number         0  stm32f10x_i2c.o ABSOLUTE
    Library\stm32f10x_iwdg.c                 0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    Library\stm32f10x_pwr.c                  0x00000000   Number         0  stm32f10x_pwr.o ABSOLUTE
    Library\stm32f10x_rcc.c                  0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    Library\stm32f10x_rtc.c                  0x00000000   Number         0  stm32f10x_rtc.o ABSOLUTE
    Library\stm32f10x_sdio.c                 0x00000000   Number         0  stm32f10x_sdio.o ABSOLUTE
    Library\stm32f10x_spi.c                  0x00000000   Number         0  stm32f10x_spi.o ABSOLUTE
    Library\stm32f10x_tim.c                  0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    Library\stm32f10x_usart.c                0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    Library\stm32f10x_wwdg.c                 0x00000000   Number         0  stm32f10x_wwdg.o ABSOLUTE
    Start\\core_cm3.c                        0x00000000   Number         0  core_cm3.o ABSOLUTE
    Start\core_cm3.c                         0x00000000   Number         0  core_cm3.o ABSOLUTE
    Start\startup_stm32f10x_md.s             0x00000000   Number         0  startup_stm32f10x_md.o ABSOLUTE
    Start\system_stm32f10x.c                 0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    System\delay.c                           0x00000000   Number         0  delay.o ABSOLUTE
    User\clock_display.c                     0x00000000   Number         0  clock_display.o ABSOLUTE
    User\key.c                               0x00000000   Number         0  key.o ABSOLUTE
    User\led.c                               0x00000000   Number         0  led.o ABSOLUTE
    User\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    User\rtc.c                               0x00000000   Number         0  rtc.o ABSOLUTE
    User\stm32f10x_it.c                      0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    User\timer.c                             0x00000000   Number         0  timer.o ABSOLUTE
    User\uart_protocol.c                     0x00000000   Number         0  uart_protocol.o ABSOLUTE
    User\usart.c                             0x00000000   Number         0  usart.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f10x_md.o(RESET)
    !!!main                                  0x080000ec   Section        8  __main.o(!!!main)
    !!!scatter                               0x080000f4   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x08000128   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000144   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x08000160   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000009  0x08000160   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000C  0x08000166   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$00000014  0x0800016c   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000017  0x08000172   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x08000176   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x08000178   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x08000178   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000178   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000178   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000178   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x08000178   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x0800017e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x0800017e   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x0800018a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x0800018a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x0800018a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x0800018a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x0800018a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x0800018a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x0800018a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x0800018a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x0800018a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x0800018a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x0800018a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x0800018a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x0800018a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x0800018a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x0800018a   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x0800018c   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x0800018e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x0800018e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x0800018e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x0800018e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x0800018e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x0800018e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x0800018e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x0800018e   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x08000190   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000190   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000190   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000196   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000196   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0800019a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0800019a   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080001a2   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080001a4   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080001a4   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080001a8   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080001b0   Section       64  startup_stm32f10x_md.o(.text)
    .text                                    0x080001f0   Section        2  use_no_semi_2.o(.text)
    .text                                    0x080001f4   Section        0  noretval__2printf.o(.text)
    .text                                    0x0800020c   Section        0  noretval__2sprintf.o(.text)
    .text                                    0x08000234   Section        0  _printf_pad.o(.text)
    .text                                    0x08000282   Section        0  _printf_str.o(.text)
    .text                                    0x080002d4   Section        0  _printf_dec.o(.text)
    .text                                    0x0800034c   Section        0  _printf_hex_int.o(.text)
    .text                                    0x080003a4   Section        0  __printf_flags_wp.o(.text)
    .text                                    0x080004dc   Section        0  __0sscanf.o(.text)
    .text                                    0x08000518   Section        0  _scanf_int.o(.text)
    .text                                    0x08000664   Section        0  _scanf_str.o(.text)
    .text                                    0x08000744   Section        0  atoi.o(.text)
    .text                                    0x0800075e   Section        0  strchr.o(.text)
    .text                                    0x08000772   Section        0  strcpy.o(.text)
    .text                                    0x080007ba   Section        0  strlen.o(.text)
    .text                                    0x080007f8   Section        0  strncmp.o(.text)
    .text                                    0x0800088e   Section        0  strcat.o(.text)
    .text                                    0x080008a6   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x08000930   Section       68  rt_memclr.o(.text)
    .text                                    0x08000974   Section       78  rt_memclr_w.o(.text)
    .text                                    0x080009c2   Section       86  strncpy.o(.text)
    .text                                    0x08000a18   Section      128  strcmpv7m.o(.text)
    .text                                    0x08000a98   Section        0  heapauxi.o(.text)
    .text                                    0x08000a9e   Section        2  use_no_semi.o(.text)
    .text                                    0x08000aa0   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08000aa8   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08000b5c   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000b5d   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000b8c   Section        0  _sputc.o(.text)
    .text                                    0x08000b96   Section        0  _printf_char.o(.text)
    .text                                    0x08000bc4   Section        0  _printf_char_file.o(.text)
    .text                                    0x08000be8   Section        0  _chval.o(.text)
    .text                                    0x08000c04   Section        0  scanf_char.o(.text)
    _scanf_char_input                        0x08000c05   Thumb Code    12  scanf_char.o(.text)
    .text                                    0x08000c30   Section        0  _sgetc.o(.text)
    .text                                    0x08000c70   Section        0  strtol.o(.text)
    .text                                    0x08000ce0   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x08000d44   Section        8  libspace.o(.text)
    .text                                    0x08000d4c   Section       16  rt_ctype_table.o(.text)
    .text                                    0x08000d5c   Section        0  isspace.o(.text)
    .text                                    0x08000d70   Section        0  _scanf.o(.text)
    .text                                    0x080010e4   Section        0  _strtoul.o(.text)
    .text                                    0x08001182   Section        0  ferror.o(.text)
    .text                                    0x0800118a   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x080011d4   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x080011dc   Section        0  exit.o(.text)
    i.Alarm_Add                              0x080011ee   Section        0  rtc.o(i.Alarm_Add)
    i.Alarm_Check                            0x08001224   Section        0  rtc.o(i.Alarm_Check)
    i.Alarm_Init                             0x08001300   Section        0  rtc.o(i.Alarm_Init)
    i.BusFault_Handler                       0x0800130e   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.Clock_ChangePage                       0x08001314   Section        0  clock_display.o(i.Clock_ChangePage)
    i.Clock_Display                          0x08001354   Section        0  clock_display.o(i.Clock_Display)
    i.Clock_DrawAlarmEdit                    0x080013b0   Section        0  clock_display.o(i.Clock_DrawAlarmEdit)
    i.Clock_DrawAlarmList                    0x08001b74   Section        0  clock_display.o(i.Clock_DrawAlarmList)
    i.Clock_DrawDigitalTime                  0x08001e40   Section        0  clock_display.o(i.Clock_DrawDigitalTime)
    i.Clock_DrawTimeSet                      0x08002094   Section        0  clock_display.o(i.Clock_DrawTimeSet)
    i.Clock_Init                             0x08002748   Section        0  clock_display.o(i.Clock_Init)
    i.Clock_ProcessKey                       0x0800276c   Section        0  clock_display.o(i.Clock_ProcessKey)
    i.Clock_RefreshTimeDigit                 0x08002fc0   Section        0  clock_display.o(i.Clock_RefreshTimeDigit)
    i.DebugMon_Handler                       0x080031ac   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.GPIO_Init                              0x080031ae   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_PinRemapConfig                    0x080032c4   Section        0  stm32f10x_gpio.o(i.GPIO_PinRemapConfig)
    i.GPIO_ReadInputDataBit                  0x08003354   Section        0  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    i.GPIO_SetBits                           0x08003366   Section        0  stm32f10x_gpio.o(i.GPIO_SetBits)
    i.GetDayOfWeek                           0x0800336a   Section        0  clock_display.o(i.GetDayOfWeek)
    i.HardFault_Handler                      0x080033e8   Section        0  stm32f10x_it.o(i.HardFault_Handler)
    i.IsLeapYear                             0x080033ec   Section        0  rtc.o(i.IsLeapYear)
    i.KEY_Init                               0x08003420   Section        0  key.o(i.KEY_Init)
    i.KEY_Scan                               0x08003478   Section        0  key.o(i.KEY_Scan)
    i.LCD_Clear                              0x0800351c   Section        0  lcd.o(i.LCD_Clear)
    i.LCD_Display_Dir                        0x080035a4   Section        0  lcd.o(i.LCD_Display_Dir)
    i.LCD_DrawLine                           0x08003764   Section        0  lcd.o(i.LCD_DrawLine)
    i.LCD_DrawPoint                          0x08003814   Section        0  lcd.o(i.LCD_DrawPoint)
    i.LCD_DrawRectangle                      0x08003854   Section        0  lcd.o(i.LCD_DrawRectangle)
    i.LCD_Fast_DrawPoint                     0x08003890   Section        0  lcd.o(i.LCD_Fast_DrawPoint)
    i.LCD_Fill                               0x08003bf4   Section        0  lcd.o(i.LCD_Fill)
    i.LCD_Init                               0x08003cc8   Section        0  lcd.o(i.LCD_Init)
    i.LCD_RD_DATA                            0x08007884   Section        0  lcd.o(i.LCD_RD_DATA)
    i.LCD_ReadReg                            0x080078f4   Section        0  lcd.o(i.LCD_ReadReg)
    i.LCD_SSD_BackLightSet                   0x08007904   Section        0  lcd.o(i.LCD_SSD_BackLightSet)
    i.LCD_Scan_Dir                           0x080079d8   Section        0  lcd.o(i.LCD_Scan_Dir)
    i.LCD_SetCursor                          0x08007e30   Section        0  lcd.o(i.LCD_SetCursor)
    i.LCD_ShowChar                           0x080081ec   Section        0  lcd.o(i.LCD_ShowChar)
    i.LCD_ShowString                         0x08008314   Section        0  lcd.o(i.LCD_ShowString)
    i.LCD_WR_DATAX                           0x0800837c   Section        0  lcd.o(i.LCD_WR_DATAX)
    i.LCD_WR_REG                             0x080083a0   Section        0  lcd.o(i.LCD_WR_REG)
    i.LCD_WriteRAM_Prepare                   0x080083c4   Section        0  lcd.o(i.LCD_WriteRAM_Prepare)
    i.LCD_WriteReg                           0x080083d4   Section        0  lcd.o(i.LCD_WriteReg)
    i.LED1_Off                               0x08008404   Section        0  led.o(i.LED1_Off)
    i.LED1_On                                0x08008410   Section        0  led.o(i.LED1_On)
    i.LED1_Toggle                            0x0800841c   Section        0  led.o(i.LED1_Toggle)
    i.LED2_Off                               0x08008434   Section        0  led.o(i.LED2_Off)
    i.LED2_On                                0x08008440   Section        0  led.o(i.LED2_On)
    i.LED2_Toggle                            0x0800844c   Section        0  led.o(i.LED2_Toggle)
    i.LED_Init                               0x08008464   Section        0  led.o(i.LED_Init)
    i.LED_StartBlink                         0x080084c0   Section        0  led.o(i.LED_StartBlink)
    i.LED_StopBlink                          0x08008508   Section        0  led.o(i.LED_StopBlink)
    i.MemManage_Handler                      0x08008520   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08008524   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x08008528   Section        0  misc.o(i.NVIC_Init)
    i.PendSV_Handler                         0x08008598   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.RCC_APB1PeriphClockCmd                 0x0800859c   Section        0  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x080085bc   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x080085dc   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.RTC_Init                               0x080086b0   Section        0  rtc.o(i.RTC_Init)
    i.RTC_SetTime                            0x080086dc   Section        0  rtc.o(i.RTC_SetTime)
    i.SVC_Handler                            0x080086f0   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.SelectCallback_AddAlarm                0x080086f4   Section        0  clock_display.o(i.SelectCallback_AddAlarm)
    SelectCallback_AddAlarm                  0x080086f5   Thumb Code    26  clock_display.o(i.SelectCallback_AddAlarm)
    i.SelectCallback_CancelEdit              0x0800871c   Section        0  clock_display.o(i.SelectCallback_CancelEdit)
    SelectCallback_CancelEdit                0x0800871d   Thumb Code    24  clock_display.o(i.SelectCallback_CancelEdit)
    i.SelectCallback_CancelTimeSet           0x08008740   Section        0  clock_display.o(i.SelectCallback_CancelTimeSet)
    SelectCallback_CancelTimeSet             0x08008741   Thumb Code    26  clock_display.o(i.SelectCallback_CancelTimeSet)
    i.SelectCallback_EditAlarm               0x08008768   Section        0  clock_display.o(i.SelectCallback_EditAlarm)
    SelectCallback_EditAlarm                 0x08008769   Thumb Code    22  clock_display.o(i.SelectCallback_EditAlarm)
    i.SelectCallback_GotoClock               0x08008788   Section        0  clock_display.o(i.SelectCallback_GotoClock)
    SelectCallback_GotoClock                 0x08008789   Thumb Code    10  clock_display.o(i.SelectCallback_GotoClock)
    i.SelectCallback_SaveAlarm               0x08008794   Section        0  clock_display.o(i.SelectCallback_SaveAlarm)
    SelectCallback_SaveAlarm                 0x08008795   Thumb Code   156  clock_display.o(i.SelectCallback_SaveAlarm)
    i.SelectCallback_SaveTime                0x080088d8   Section        0  clock_display.o(i.SelectCallback_SaveTime)
    SelectCallback_SaveTime                  0x080088d9   Thumb Code    46  clock_display.o(i.SelectCallback_SaveTime)
    i.SelectCallback_SetTime                 0x08008940   Section        0  clock_display.o(i.SelectCallback_SetTime)
    SelectCallback_SetTime                   0x08008941   Thumb Code    16  clock_display.o(i.SelectCallback_SetTime)
    i.SetSysClock                            0x08008954   Section        0  system_stm32f10x.o(i.SetSysClock)
    SetSysClock                              0x08008955   Thumb Code     8  system_stm32f10x.o(i.SetSysClock)
    i.SetSysClockTo72                        0x0800895c   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x0800895d   Thumb Code   214  system_stm32f10x.o(i.SetSysClockTo72)
    i.SystemInit                             0x08008a3c   Section        0  system_stm32f10x.o(i.SystemInit)
    i.TIM2_IRQHandler                        0x08008a9c   Section        0  stm32f10x_it.o(i.TIM2_IRQHandler)
    i.TIM2_Init                              0x08008c2c   Section        0  timer.o(i.TIM2_Init)
    i.TIM3_IRQHandler                        0x08008c88   Section        0  stm32f10x_it.o(i.TIM3_IRQHandler)
    i.TIM3_Init                              0x08008d10   Section        0  timer.o(i.TIM3_Init)
    i.TIM4_Init                              0x08008d70   Section        0  timer.o(i.TIM4_Init)
    i.TIM_ClearITPendingBit                  0x08008da8   Section        0  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    i.TIM_Cmd                                0x08008dae   Section        0  stm32f10x_tim.o(i.TIM_Cmd)
    i.TIM_GetCounter                         0x08008dc6   Section        0  stm32f10x_tim.o(i.TIM_GetCounter)
    i.TIM_GetITStatus                        0x08008dcc   Section        0  stm32f10x_tim.o(i.TIM_GetITStatus)
    i.TIM_ITConfig                           0x08008dee   Section        0  stm32f10x_tim.o(i.TIM_ITConfig)
    i.TIM_SetCounter                         0x08008e00   Section        0  stm32f10x_tim.o(i.TIM_SetCounter)
    i.TIM_TimeBaseInit                       0x08008e04   Section        0  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    i.UART_Parse_Alarm                       0x08008ea8   Section        0  uart_protocol.o(i.UART_Parse_Alarm)
    i.UART_Parse_Command                     0x08009118   Section        0  uart_protocol.o(i.UART_Parse_Command)
    i.UART_Parse_Time                        0x080091a0   Section        0  uart_protocol.o(i.UART_Parse_Time)
    i.UART_Protocol_Init                     0x08009370   Section        0  uart_protocol.o(i.UART_Protocol_Init)
    i.UART_Protocol_Process                  0x08009424   Section        0  uart_protocol.o(i.UART_Protocol_Process)
    i.UART_Send_Alarm                        0x0800954c   Section        0  uart_protocol.o(i.UART_Send_Alarm)
    i.UART_Send_Help                         0x080096f0   Section        0  uart_protocol.o(i.UART_Send_Help)
    i.UART_Send_Time                         0x08009db4   Section        0  uart_protocol.o(i.UART_Send_Time)
    i.USART1_IRQHandler                      0x08009e10   Section        0  usart.o(i.USART1_IRQHandler)
    i.USART_Cmd                              0x08009ec4   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_GetFlagStatus                    0x08009edc   Section        0  stm32f10x_usart.o(i.USART_GetFlagStatus)
    i.USART_GetITStatus                      0x08009ef6   Section        0  stm32f10x_usart.o(i.USART_GetITStatus)
    i.USART_ITConfig                         0x08009f4a   Section        0  stm32f10x_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x08009f94   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.USART_ReceiveData                      0x0800a06c   Section        0  stm32f10x_usart.o(i.USART_ReceiveData)
    i.USART_SendData                         0x0800a076   Section        0  stm32f10x_usart.o(i.USART_SendData)
    i.USART_SendString                       0x0800a07e   Section        0  usart.o(i.USART_SendString)
    i.UsageFault_Handler                     0x0800a0b4   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i._is_digit                              0x0800a0b8   Section        0  __printf_wp.o(i._is_digit)
    i._sys_exit                              0x0800a0c6   Section        0  usart.o(i._sys_exit)
    i.delay_ms                               0x0800a0ca   Section        0  timer.o(i.delay_ms)
    i.delay_us                               0x0800a0e4   Section        0  timer.o(i.delay_us)
    i.fputc                                  0x0800a104   Section        0  usart.o(i.fputc)
    i.main                                   0x0800a128   Section        0  main.o(i.main)
    i.uart1_init                             0x0800a304   Section        0  usart.o(i.uart1_init)
    locale$$code                             0x0800a3a4   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$dfixu                              0x0800a3d0   Section       90  dfixu.o(x$fpl$dfixu)
    x$fpl$dfltu                              0x0800a42a   Section       38  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dmul                               0x0800a450   Section      340  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x0800a5a4   Section      156  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x0800a640   Section       12  dretinf.o(x$fpl$dretinf)
    .constdata                               0x0800a64c   Section     6080  lcd.o(.constdata)
    x$fpl$usenofp                            0x0800a64c   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x0800be0c   Section       12  rtc.o(.constdata)
    .constdata                               0x0800be18   Section       40  _printf_hex_int.o(.constdata)
    uc_hextab                                0x0800be18   Data          20  _printf_hex_int.o(.constdata)
    lc_hextab                                0x0800be2c   Data          20  _printf_hex_int.o(.constdata)
    .constdata                               0x0800be40   Section       17  __printf_flags_wp.o(.constdata)
    maptable                                 0x0800be40   Data          17  __printf_flags_wp.o(.constdata)
    .conststring                             0x0800be54   Section       93  clock_display.o(.conststring)
    locale$$data                             0x0800bed4   Section      272  lc_ctype_c.o(locale$$data)
    __lcctype_c_name                         0x0800bed8   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x0800bee0   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x0800bfe4   Data           0  lc_ctype_c.o(locale$$data)
    .data                                    0x20000000   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x20000000   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000010   Data           4  stm32f10x_rcc.o(.data)
    .data                                    0x20000014   Section        4  lcd.o(.data)
    .data                                    0x20000018   Section        1  stm32f10x_it.o(.data)
    blink_count                              0x20000018   Data           1  stm32f10x_it.o(.data)
    .data                                    0x2000001c   Section        8  usart.o(.data)
    .data                                    0x20000024   Section      128  clock_display.o(.data)
    g_CurrentPage                            0x20000024   Data           1  clock_display.o(.data)
    g_CurrentAlarmIndex                      0x20000025   Data           1  clock_display.o(.data)
    g_NeedRefresh                            0x20000026   Data           1  clock_display.o(.data)
    g_LastSecond                             0x20000027   Data           1  clock_display.o(.data)
    g_LastMinute                             0x20000028   Data           1  clock_display.o(.data)
    g_LastHour                               0x20000029   Data           1  clock_display.o(.data)
    g_LastDay                                0x2000002a   Data           1  clock_display.o(.data)
    g_LastMonth                              0x2000002b   Data           1  clock_display.o(.data)
    g_LastYear                               0x2000002c   Data           2  clock_display.o(.data)
    g_LastWeek                               0x2000002e   Data           1  clock_display.o(.data)
    g_LastUpdateTime                         0x2000002f   Data           1  clock_display.o(.data)
    g_SelectedItem                           0x20000030   Data           1  clock_display.o(.data)
    g_EditingField                           0x20000031   Data           1  clock_display.o(.data)
    g_TimeEditMode                           0x20000032   Data           1  clock_display.o(.data)
    g_FirstTimeEnter                         0x20000033   Data           1  clock_display.o(.data)
    weekdays                                 0x20000034   Data          28  clock_display.o(.data)
    weekdays                                 0x20000050   Data          28  clock_display.o(.data)
    weekdays                                 0x2000006c   Data          28  clock_display.o(.data)
    weekdays                                 0x20000088   Data          28  clock_display.o(.data)
    .data                                    0x200000a4   Section        4  rtc.o(.data)
    .data                                    0x200000a8   Section        1  key.o(.data)
    key_up                                   0x200000a8   Data           1  key.o(.data)
    .bss                                     0x200000ac   Section       14  lcd.o(.bss)
    .bss                                     0x200000ba   Section      200  usart.o(.bss)
    .bss                                     0x20000182   Section       36  clock_display.o(.bss)
    g_TempAlarm                              0x20000182   Data          25  clock_display.o(.bss)
    g_TempDateTime                           0x2000019c   Data          10  clock_display.o(.bss)
    .bss                                     0x200001a6   Section      261  rtc.o(.bss)
    .bss                                     0x200002ac   Section       96  libspace.o(.bss)
    HEAP                                     0x20000310   Section      512  startup_stm32f10x_md.o(HEAP)
    Heap_Mem                                 0x20000310   Data         512  startup_stm32f10x_md.o(HEAP)
    STACK                                    0x20000510   Section     1024  startup_stm32f10x_md.o(STACK)
    Stack_Mem                                0x20000510   Data        1024  startup_stm32f10x_md.o(STACK)
    __initial_sp                             0x20000910   Data           0  startup_stm32f10x_md.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _scanf_longlong                           - Undefined Weak Reference
    _scanf_mbtowc                             - Undefined Weak Reference
    _scanf_real                               - Undefined Weak Reference
    _scanf_wctomb                             - Undefined Weak Reference
    _scanf_wstring                            - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f10x_md.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_md.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f10x_md.o(RESET)
    __main                                   0x080000ed   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080000f5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000103   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x08000129   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000145   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_d                                0x08000161   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_percent                          0x08000161   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_x                                0x08000167   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_s                                0x0800016d   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_percent_end                      0x08000173   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x08000177   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x08000179   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_heap_1                     0x08000179   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x08000179   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x08000179   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000179   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x08000179   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x0800017f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x0800017f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_alloca_1                   0x0800018b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x0800018b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x0800018b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x0800018b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x0800018b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x0800018b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x0800018b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x0800018b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_ctype_1                 0x0800018b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x0800018b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x0800018b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x0800018b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x0800018b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x0800018b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x0800018b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x0800018d   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x0800018f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x0800018f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x0800018f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x0800018f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x0800018f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x0800018f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x0800018f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x0800018f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x08000191   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000191   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000191   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000197   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000197   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x0800019b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x0800019b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080001a3   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080001a5   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080001a5   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080001a9   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080001b1   Thumb Code     8  startup_stm32f10x_md.o(.text)
    SysTick_Handler                          0x080001c9   Thumb Code     2  startup_stm32f10x_md.o(.text)
    ADC1_2_IRQHandler                        0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_RX1_IRQHandler                      0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_SCE_IRQHandler                      0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel1_IRQHandler                 0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel2_IRQHandler                 0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel3_IRQHandler                 0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel4_IRQHandler                 0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel5_IRQHandler                 0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel6_IRQHandler                 0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel7_IRQHandler                 0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI0_IRQHandler                         0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI15_10_IRQHandler                     0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI1_IRQHandler                         0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI2_IRQHandler                         0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI3_IRQHandler                         0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI4_IRQHandler                         0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI9_5_IRQHandler                       0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    FLASH_IRQHandler                         0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_ER_IRQHandler                       0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_EV_IRQHandler                       0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_ER_IRQHandler                       0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_EV_IRQHandler                       0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    PVD_IRQHandler                           0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RCC_IRQHandler                           0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTCAlarm_IRQHandler                      0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTC_IRQHandler                           0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI1_IRQHandler                          0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI2_IRQHandler                          0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TAMPER_IRQHandler                        0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_BRK_IRQHandler                      0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_CC_IRQHandler                       0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_UP_IRQHandler                       0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM4_IRQHandler                          0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART2_IRQHandler                        0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART3_IRQHandler                        0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USBWakeUp_IRQHandler                     0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    WWDG_IRQHandler                          0x080001cb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    __user_initial_stackheap                 0x080001cd   Thumb Code     0  startup_stm32f10x_md.o(.text)
    __use_no_semihosting                     0x080001f1   Thumb Code     2  use_no_semi_2.o(.text)
    __2printf                                0x080001f5   Thumb Code    20  noretval__2printf.o(.text)
    __2sprintf                               0x0800020d   Thumb Code    34  noretval__2sprintf.o(.text)
    _printf_pre_padding                      0x08000235   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x08000261   Thumb Code    34  _printf_pad.o(.text)
    _printf_str                              0x08000283   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x080002d5   Thumb Code   104  _printf_dec.o(.text)
    _printf_int_hex                          0x0800034d   Thumb Code    84  _printf_hex_int.o(.text)
    _printf_longlong_hex                     0x0800034d   Thumb Code     0  _printf_hex_int.o(.text)
    __printf                                 0x080003a5   Thumb Code   308  __printf_flags_wp.o(.text)
    __0sscanf                                0x080004dd   Thumb Code    52  __0sscanf.o(.text)
    _scanf_int                               0x08000519   Thumb Code   332  _scanf_int.o(.text)
    _scanf_string                            0x08000665   Thumb Code   224  _scanf_str.o(.text)
    atoi                                     0x08000745   Thumb Code    26  atoi.o(.text)
    strchr                                   0x0800075f   Thumb Code    20  strchr.o(.text)
    strcpy                                   0x08000773   Thumb Code    72  strcpy.o(.text)
    strlen                                   0x080007bb   Thumb Code    62  strlen.o(.text)
    strncmp                                  0x080007f9   Thumb Code   150  strncmp.o(.text)
    strcat                                   0x0800088f   Thumb Code    24  strcat.o(.text)
    __aeabi_memcpy                           0x080008a7   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x080008a7   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x0800090d   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memclr                           0x08000931   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x08000931   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x08000935   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr4                          0x08000975   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x08000975   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x08000975   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x08000979   Thumb Code     0  rt_memclr_w.o(.text)
    strncpy                                  0x080009c3   Thumb Code    86  strncpy.o(.text)
    strcmp                                   0x08000a19   Thumb Code   128  strcmpv7m.o(.text)
    __use_two_region_memory                  0x08000a99   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08000a9b   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08000a9d   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x08000a9f   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000a9f   Thumb Code     2  use_no_semi.o(.text)
    __aeabi_errno_addr                       0x08000aa1   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08000aa1   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08000aa1   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    _printf_int_common                       0x08000aa9   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_char_common                      0x08000b67   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000b8d   Thumb Code    10  _sputc.o(.text)
    _printf_cs_common                        0x08000b97   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08000bab   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08000bbb   Thumb Code     8  _printf_char.o(.text)
    _printf_char_file                        0x08000bc5   Thumb Code    32  _printf_char_file.o(.text)
    _chval                                   0x08000be9   Thumb Code    28  _chval.o(.text)
    __vfscanf_char                           0x08000c11   Thumb Code    24  scanf_char.o(.text)
    _sgetc                                   0x08000c31   Thumb Code    30  _sgetc.o(.text)
    _sbackspace                              0x08000c4f   Thumb Code    34  _sgetc.o(.text)
    strtol                                   0x08000c71   Thumb Code   112  strtol.o(.text)
    __aeabi_memcpy4                          0x08000ce1   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x08000ce1   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x08000ce1   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x08000d29   Thumb Code     0  rt_memcpy_w.o(.text)
    __user_libspace                          0x08000d45   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000d45   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000d45   Thumb Code     0  libspace.o(.text)
    __rt_ctype_table                         0x08000d4d   Thumb Code    16  rt_ctype_table.o(.text)
    isspace                                  0x08000d5d   Thumb Code    18  isspace.o(.text)
    __vfscanf                                0x08000d71   Thumb Code   880  _scanf.o(.text)
    _strtoul                                 0x080010e5   Thumb Code   158  _strtoul.o(.text)
    ferror                                   0x08001183   Thumb Code     8  ferror.o(.text)
    __user_setup_stackheap                   0x0800118b   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_locale                              0x080011d5   Thumb Code     8  rt_locale_intlibspace.o(.text)
    exit                                     0x080011dd   Thumb Code    18  exit.o(.text)
    Alarm_Add                                0x080011ef   Thumb Code    54  rtc.o(i.Alarm_Add)
    Alarm_Check                              0x08001225   Thumb Code   160  rtc.o(i.Alarm_Check)
    Alarm_Init                               0x08001301   Thumb Code    14  rtc.o(i.Alarm_Init)
    BusFault_Handler                         0x0800130f   Thumb Code     4  stm32f10x_it.o(i.BusFault_Handler)
    Clock_ChangePage                         0x08001315   Thumb Code    42  clock_display.o(i.Clock_ChangePage)
    Clock_Display                            0x08001355   Thumb Code    78  clock_display.o(i.Clock_Display)
    Clock_DrawAlarmEdit                      0x080013b1   Thumb Code  1898  clock_display.o(i.Clock_DrawAlarmEdit)
    Clock_DrawAlarmList                      0x08001b75   Thumb Code   644  clock_display.o(i.Clock_DrawAlarmList)
    Clock_DrawDigitalTime                    0x08001e41   Thumb Code   508  clock_display.o(i.Clock_DrawDigitalTime)
    Clock_DrawTimeSet                        0x08002095   Thumb Code  1646  clock_display.o(i.Clock_DrawTimeSet)
    Clock_Init                               0x08002749   Thumb Code    28  clock_display.o(i.Clock_Init)
    Clock_ProcessKey                         0x0800276d   Thumb Code  1884  clock_display.o(i.Clock_ProcessKey)
    Clock_RefreshTimeDigit                   0x08002fc1   Thumb Code   424  clock_display.o(i.Clock_RefreshTimeDigit)
    DebugMon_Handler                         0x080031ad   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    GPIO_Init                                0x080031af   Thumb Code   278  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_PinRemapConfig                      0x080032c5   Thumb Code   138  stm32f10x_gpio.o(i.GPIO_PinRemapConfig)
    GPIO_ReadInputDataBit                    0x08003355   Thumb Code    18  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    GPIO_SetBits                             0x08003367   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_SetBits)
    GetDayOfWeek                             0x0800336b   Thumb Code   126  clock_display.o(i.GetDayOfWeek)
    HardFault_Handler                        0x080033e9   Thumb Code     4  stm32f10x_it.o(i.HardFault_Handler)
    IsLeapYear                               0x080033ed   Thumb Code    50  rtc.o(i.IsLeapYear)
    KEY_Init                                 0x08003421   Thumb Code    80  key.o(i.KEY_Init)
    KEY_Scan                                 0x08003479   Thumb Code   150  key.o(i.KEY_Scan)
    LCD_Clear                                0x0800351d   Thumb Code   122  lcd.o(i.LCD_Clear)
    LCD_Display_Dir                          0x080035a5   Thumb Code   444  lcd.o(i.LCD_Display_Dir)
    LCD_DrawLine                             0x08003765   Thumb Code   176  lcd.o(i.LCD_DrawLine)
    LCD_DrawPoint                            0x08003815   Thumb Code    52  lcd.o(i.LCD_DrawPoint)
    LCD_DrawRectangle                        0x08003855   Thumb Code    60  lcd.o(i.LCD_DrawRectangle)
    LCD_Fast_DrawPoint                       0x08003891   Thumb Code   854  lcd.o(i.LCD_Fast_DrawPoint)
    LCD_Fill                                 0x08003bf5   Thumb Code   200  lcd.o(i.LCD_Fill)
    LCD_Init                                 0x08003cc9   Thumb Code 15292  lcd.o(i.LCD_Init)
    LCD_RD_DATA                              0x08007885   Thumb Code    98  lcd.o(i.LCD_RD_DATA)
    LCD_ReadReg                              0x080078f5   Thumb Code    16  lcd.o(i.LCD_ReadReg)
    LCD_SSD_BackLightSet                     0x08007905   Thumb Code   198  lcd.o(i.LCD_SSD_BackLightSet)
    LCD_Scan_Dir                             0x080079d9   Thumb Code  1112  lcd.o(i.LCD_Scan_Dir)
    LCD_SetCursor                            0x08007e31   Thumb Code   944  lcd.o(i.LCD_SetCursor)
    LCD_ShowChar                             0x080081ed   Thumb Code   272  lcd.o(i.LCD_ShowChar)
    LCD_ShowString                           0x08008315   Thumb Code   102  lcd.o(i.LCD_ShowString)
    LCD_WR_DATAX                             0x0800837d   Thumb Code    28  lcd.o(i.LCD_WR_DATAX)
    LCD_WR_REG                               0x080083a1   Thumb Code    28  lcd.o(i.LCD_WR_REG)
    LCD_WriteRAM_Prepare                     0x080083c5   Thumb Code    12  lcd.o(i.LCD_WriteRAM_Prepare)
    LCD_WriteReg                             0x080083d5   Thumb Code    40  lcd.o(i.LCD_WriteReg)
    LED1_Off                                 0x08008405   Thumb Code     8  led.o(i.LED1_Off)
    LED1_On                                  0x08008411   Thumb Code     8  led.o(i.LED1_On)
    LED1_Toggle                              0x0800841d   Thumb Code    18  led.o(i.LED1_Toggle)
    LED2_Off                                 0x08008435   Thumb Code     8  led.o(i.LED2_Off)
    LED2_On                                  0x08008441   Thumb Code     8  led.o(i.LED2_On)
    LED2_Toggle                              0x0800844d   Thumb Code    18  led.o(i.LED2_Toggle)
    LED_Init                                 0x08008465   Thumb Code    82  led.o(i.LED_Init)
    LED_StartBlink                           0x080084c1   Thumb Code    42  led.o(i.LED_StartBlink)
    LED_StopBlink                            0x08008509   Thumb Code    20  led.o(i.LED_StopBlink)
    MemManage_Handler                        0x08008521   Thumb Code     4  stm32f10x_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08008525   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    NVIC_Init                                0x08008529   Thumb Code   100  misc.o(i.NVIC_Init)
    PendSV_Handler                           0x08008599   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    RCC_APB1PeriphClockCmd                   0x0800859d   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x080085bd   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x080085dd   Thumb Code   192  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    RTC_Init                                 0x080086b1   Thumb Code    36  rtc.o(i.RTC_Init)
    RTC_SetTime                              0x080086dd   Thumb Code    16  rtc.o(i.RTC_SetTime)
    SVC_Handler                              0x080086f1   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    SystemInit                               0x08008a3d   Thumb Code    78  system_stm32f10x.o(i.SystemInit)
    TIM2_IRQHandler                          0x08008a9d   Thumb Code   308  stm32f10x_it.o(i.TIM2_IRQHandler)
    TIM2_Init                                0x08008c2d   Thumb Code    90  timer.o(i.TIM2_Init)
    TIM3_IRQHandler                          0x08008c89   Thumb Code    92  stm32f10x_it.o(i.TIM3_IRQHandler)
    TIM3_Init                                0x08008d11   Thumb Code    90  timer.o(i.TIM3_Init)
    TIM4_Init                                0x08008d71   Thumb Code    52  timer.o(i.TIM4_Init)
    TIM_ClearITPendingBit                    0x08008da9   Thumb Code     6  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    TIM_Cmd                                  0x08008daf   Thumb Code    24  stm32f10x_tim.o(i.TIM_Cmd)
    TIM_GetCounter                           0x08008dc7   Thumb Code     6  stm32f10x_tim.o(i.TIM_GetCounter)
    TIM_GetITStatus                          0x08008dcd   Thumb Code    34  stm32f10x_tim.o(i.TIM_GetITStatus)
    TIM_ITConfig                             0x08008def   Thumb Code    18  stm32f10x_tim.o(i.TIM_ITConfig)
    TIM_SetCounter                           0x08008e01   Thumb Code     4  stm32f10x_tim.o(i.TIM_SetCounter)
    TIM_TimeBaseInit                         0x08008e05   Thumb Code   122  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    UART_Parse_Alarm                         0x08008ea9   Thumb Code   346  uart_protocol.o(i.UART_Parse_Alarm)
    UART_Parse_Command                       0x08009119   Thumb Code    88  uart_protocol.o(i.UART_Parse_Command)
    UART_Parse_Time                          0x080091a1   Thumb Code   250  uart_protocol.o(i.UART_Parse_Time)
    UART_Protocol_Init                       0x08009371   Thumb Code    28  uart_protocol.o(i.UART_Protocol_Init)
    UART_Protocol_Process                    0x08009425   Thumb Code   158  uart_protocol.o(i.UART_Protocol_Process)
    UART_Send_Alarm                          0x0800954d   Thumb Code   260  uart_protocol.o(i.UART_Send_Alarm)
    UART_Send_Help                           0x080096f1   Thumb Code  1312  uart_protocol.o(i.UART_Send_Help)
    UART_Send_Time                           0x08009db5   Thumb Code    42  uart_protocol.o(i.UART_Send_Time)
    USART1_IRQHandler                        0x08009e11   Thumb Code   168  usart.o(i.USART1_IRQHandler)
    USART_Cmd                                0x08009ec5   Thumb Code    24  stm32f10x_usart.o(i.USART_Cmd)
    USART_GetFlagStatus                      0x08009edd   Thumb Code    26  stm32f10x_usart.o(i.USART_GetFlagStatus)
    USART_GetITStatus                        0x08009ef7   Thumb Code    84  stm32f10x_usart.o(i.USART_GetITStatus)
    USART_ITConfig                           0x08009f4b   Thumb Code    74  stm32f10x_usart.o(i.USART_ITConfig)
    USART_Init                               0x08009f95   Thumb Code   210  stm32f10x_usart.o(i.USART_Init)
    USART_ReceiveData                        0x0800a06d   Thumb Code    10  stm32f10x_usart.o(i.USART_ReceiveData)
    USART_SendData                           0x0800a077   Thumb Code     8  stm32f10x_usart.o(i.USART_SendData)
    USART_SendString                         0x0800a07f   Thumb Code    54  usart.o(i.USART_SendString)
    UsageFault_Handler                       0x0800a0b5   Thumb Code     4  stm32f10x_it.o(i.UsageFault_Handler)
    _is_digit                                0x0800a0b9   Thumb Code    14  __printf_wp.o(i._is_digit)
    _sys_exit                                0x0800a0c7   Thumb Code     4  usart.o(i._sys_exit)
    delay_ms                                 0x0800a0cb   Thumb Code    26  timer.o(i.delay_ms)
    delay_us                                 0x0800a0e5   Thumb Code    26  timer.o(i.delay_us)
    fputc                                    0x0800a105   Thumb Code    32  usart.o(i.fputc)
    main                                     0x0800a129   Thumb Code   272  main.o(i.main)
    uart1_init                               0x0800a305   Thumb Code   152  usart.o(i.uart1_init)
    _get_lc_ctype                            0x0800a3a5   Thumb Code    44  lc_ctype_c.o(locale$$code)
    __aeabi_d2uiz                            0x0800a3d1   Thumb Code     0  dfixu.o(x$fpl$dfixu)
    _dfixu                                   0x0800a3d1   Thumb Code    90  dfixu.o(x$fpl$dfixu)
    __aeabi_ui2d                             0x0800a42b   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x0800a42b   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_dmul                             0x0800a451   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x0800a451   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x0800a5a5   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x0800a641   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __I$use$fp                               0x0800a64c   Number         0  usenofp.o(x$fpl$usenofp)
    asc2_1206                                0x0800a64c   Data        1140  lcd.o(.constdata)
    asc2_1608                                0x0800aac0   Data        1520  lcd.o(.constdata)
    asc2_2412                                0x0800b0b0   Data        3420  lcd.o(.constdata)
    g_MonthDays                              0x0800be0c   Data          12  rtc.o(.constdata)
    Region$$Table$$Base                      0x0800beb4   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800bed4   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x0800bee1   Data           0  lc_ctype_c.o(locale$$data)
    POINT_COLOR                              0x20000014   Data           2  lcd.o(.data)
    BACK_COLOR                               0x20000016   Data           2  lcd.o(.data)
    USART_RX_STA                             0x2000001c   Data           2  usart.o(.data)
    __stdout                                 0x20000020   Data           4  usart.o(.data)
    g_TimerCounter                           0x200000a4   Data           4  rtc.o(.data)
    lcddev                                   0x200000ac   Data          14  lcd.o(.bss)
    USART_RX_BUF                             0x200000ba   Data         200  usart.o(.bss)
    g_DateTime                               0x200001a6   Data          10  rtc.o(.bss)
    g_AlarmManager                           0x200001b0   Data         251  rtc.o(.bss)
    __libspace_start                         0x200002ac   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x2000030c   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000c090, Max: 0x00040000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000bfe4, Max: 0x00040000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000ec   Data   RO         3138    RESET               startup_stm32f10x_md.o
    0x080000ec   0x080000ec   0x00000008   Code   RO         4083  * !!!main             c_w.l(__main.o)
    0x080000f4   0x080000f4   0x00000034   Code   RO         4385    !!!scatter          c_w.l(__scatter.o)
    0x08000128   0x08000128   0x0000001a   Code   RO         4387    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000142   0x08000142   0x00000002   PAD
    0x08000144   0x08000144   0x0000001c   Code   RO         4389    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000160   0x08000160   0x00000000   Code   RO         4052    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x08000160   0x08000160   0x00000006   Code   RO         4051    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000166   0x08000166   0x00000006   Code   RO         4050    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x0800016c   0x0800016c   0x00000006   Code   RO         4049    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x08000172   0x08000172   0x00000004   Code   RO         4151    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x08000176   0x08000176   0x00000002   Code   RO         4254    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000178   0x08000178   0x00000000   Code   RO         4260    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x08000178   0x08000178   0x00000000   Code   RO         4262    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000178   0x08000178   0x00000000   Code   RO         4265    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000178   0x08000178   0x00000000   Code   RO         4267    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000178   0x08000178   0x00000000   Code   RO         4269    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000178   0x08000178   0x00000006   Code   RO         4270    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x0800017e   0x0800017e   0x00000000   Code   RO         4272    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x0800017e   0x0800017e   0x0000000c   Code   RO         4273    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x0800018a   0x0800018a   0x00000000   Code   RO         4274    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x0800018a   0x0800018a   0x00000000   Code   RO         4276    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x0800018a   0x0800018a   0x00000000   Code   RO         4278    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x0800018a   0x0800018a   0x00000000   Code   RO         4280    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x0800018a   0x0800018a   0x00000000   Code   RO         4282    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x0800018a   0x0800018a   0x00000000   Code   RO         4284    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x0800018a   0x0800018a   0x00000000   Code   RO         4286    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x0800018a   0x0800018a   0x00000000   Code   RO         4288    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x0800018a   0x0800018a   0x00000000   Code   RO         4290    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x0800018a   0x0800018a   0x00000000   Code   RO         4292    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x0800018a   0x0800018a   0x00000000   Code   RO         4296    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x0800018a   0x0800018a   0x00000000   Code   RO         4298    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x0800018a   0x0800018a   0x00000000   Code   RO         4300    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x0800018a   0x0800018a   0x00000000   Code   RO         4302    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x0800018a   0x0800018a   0x00000002   Code   RO         4303    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x0800018c   0x0800018c   0x00000002   Code   RO         4325    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x0800018e   0x0800018e   0x00000000   Code   RO         4335    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x0800018e   0x0800018e   0x00000000   Code   RO         4337    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x0800018e   0x0800018e   0x00000000   Code   RO         4339    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x0800018e   0x0800018e   0x00000000   Code   RO         4342    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x0800018e   0x0800018e   0x00000000   Code   RO         4345    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x0800018e   0x0800018e   0x00000000   Code   RO         4347    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x0800018e   0x0800018e   0x00000000   Code   RO         4350    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x0800018e   0x0800018e   0x00000002   Code   RO         4351    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x08000190   0x08000190   0x00000000   Code   RO         4133    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000190   0x08000190   0x00000000   Code   RO         4203    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000190   0x08000190   0x00000006   Code   RO         4215    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x08000196   0x08000196   0x00000000   Code   RO         4205    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x08000196   0x08000196   0x00000004   Code   RO         4206    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x0800019a   0x0800019a   0x00000000   Code   RO         4208    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x0800019a   0x0800019a   0x00000008   Code   RO         4209    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080001a2   0x080001a2   0x00000002   Code   RO         4257    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080001a4   0x080001a4   0x00000000   Code   RO         4307    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080001a4   0x080001a4   0x00000004   Code   RO         4308    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080001a8   0x080001a8   0x00000006   Code   RO         4309    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080001ae   0x080001ae   0x00000002   PAD
    0x080001b0   0x080001b0   0x00000040   Code   RO         3139    .text               startup_stm32f10x_md.o
    0x080001f0   0x080001f0   0x00000002   Code   RO         3990    .text               c_w.l(use_no_semi_2.o)
    0x080001f2   0x080001f2   0x00000002   PAD
    0x080001f4   0x080001f4   0x00000018   Code   RO         3996    .text               c_w.l(noretval__2printf.o)
    0x0800020c   0x0800020c   0x00000028   Code   RO         3998    .text               c_w.l(noretval__2sprintf.o)
    0x08000234   0x08000234   0x0000004e   Code   RO         4002    .text               c_w.l(_printf_pad.o)
    0x08000282   0x08000282   0x00000052   Code   RO         4004    .text               c_w.l(_printf_str.o)
    0x080002d4   0x080002d4   0x00000078   Code   RO         4006    .text               c_w.l(_printf_dec.o)
    0x0800034c   0x0800034c   0x00000058   Code   RO         4011    .text               c_w.l(_printf_hex_int.o)
    0x080003a4   0x080003a4   0x00000138   Code   RO         4041    .text               c_w.l(__printf_flags_wp.o)
    0x080004dc   0x080004dc   0x0000003c   Code   RO         4053    .text               c_w.l(__0sscanf.o)
    0x08000518   0x08000518   0x0000014c   Code   RO         4055    .text               c_w.l(_scanf_int.o)
    0x08000664   0x08000664   0x000000e0   Code   RO         4057    .text               c_w.l(_scanf_str.o)
    0x08000744   0x08000744   0x0000001a   Code   RO         4059    .text               c_w.l(atoi.o)
    0x0800075e   0x0800075e   0x00000014   Code   RO         4061    .text               c_w.l(strchr.o)
    0x08000772   0x08000772   0x00000048   Code   RO         4063    .text               c_w.l(strcpy.o)
    0x080007ba   0x080007ba   0x0000003e   Code   RO         4065    .text               c_w.l(strlen.o)
    0x080007f8   0x080007f8   0x00000096   Code   RO         4067    .text               c_w.l(strncmp.o)
    0x0800088e   0x0800088e   0x00000018   Code   RO         4069    .text               c_w.l(strcat.o)
    0x080008a6   0x080008a6   0x0000008a   Code   RO         4071    .text               c_w.l(rt_memcpy_v6.o)
    0x08000930   0x08000930   0x00000044   Code   RO         4073    .text               c_w.l(rt_memclr.o)
    0x08000974   0x08000974   0x0000004e   Code   RO         4075    .text               c_w.l(rt_memclr_w.o)
    0x080009c2   0x080009c2   0x00000056   Code   RO         4077    .text               c_w.l(strncpy.o)
    0x08000a18   0x08000a18   0x00000080   Code   RO         4079    .text               c_w.l(strcmpv7m.o)
    0x08000a98   0x08000a98   0x00000006   Code   RO         4081    .text               c_w.l(heapauxi.o)
    0x08000a9e   0x08000a9e   0x00000002   Code   RO         4131    .text               c_w.l(use_no_semi.o)
    0x08000aa0   0x08000aa0   0x00000008   Code   RO         4137    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x08000aa8   0x08000aa8   0x000000b2   Code   RO         4141    .text               c_w.l(_printf_intcommon.o)
    0x08000b5a   0x08000b5a   0x00000002   PAD
    0x08000b5c   0x08000b5c   0x00000030   Code   RO         4143    .text               c_w.l(_printf_char_common.o)
    0x08000b8c   0x08000b8c   0x0000000a   Code   RO         4145    .text               c_w.l(_sputc.o)
    0x08000b96   0x08000b96   0x0000002c   Code   RO         4147    .text               c_w.l(_printf_char.o)
    0x08000bc2   0x08000bc2   0x00000002   PAD
    0x08000bc4   0x08000bc4   0x00000024   Code   RO         4149    .text               c_w.l(_printf_char_file.o)
    0x08000be8   0x08000be8   0x0000001c   Code   RO         4152    .text               c_w.l(_chval.o)
    0x08000c04   0x08000c04   0x0000002c   Code   RO         4154    .text               c_w.l(scanf_char.o)
    0x08000c30   0x08000c30   0x00000040   Code   RO         4156    .text               c_w.l(_sgetc.o)
    0x08000c70   0x08000c70   0x00000070   Code   RO         4158    .text               c_w.l(strtol.o)
    0x08000ce0   0x08000ce0   0x00000064   Code   RO         4160    .text               c_w.l(rt_memcpy_w.o)
    0x08000d44   0x08000d44   0x00000008   Code   RO         4199    .text               c_w.l(libspace.o)
    0x08000d4c   0x08000d4c   0x00000010   Code   RO         4217    .text               c_w.l(rt_ctype_table.o)
    0x08000d5c   0x08000d5c   0x00000012   Code   RO         4219    .text               c_w.l(isspace.o)
    0x08000d6e   0x08000d6e   0x00000002   PAD
    0x08000d70   0x08000d70   0x00000374   Code   RO         4223    .text               c_w.l(_scanf.o)
    0x080010e4   0x080010e4   0x0000009e   Code   RO         4225    .text               c_w.l(_strtoul.o)
    0x08001182   0x08001182   0x00000008   Code   RO         4227    .text               c_w.l(ferror.o)
    0x0800118a   0x0800118a   0x0000004a   Code   RO         4237    .text               c_w.l(sys_stackheap_outer.o)
    0x080011d4   0x080011d4   0x00000008   Code   RO         4242    .text               c_w.l(rt_locale_intlibspace.o)
    0x080011dc   0x080011dc   0x00000012   Code   RO         4247    .text               c_w.l(exit.o)
    0x080011ee   0x080011ee   0x00000036   Code   RO         3805    i.Alarm_Add         rtc.o
    0x08001224   0x08001224   0x000000dc   Code   RO         3806    i.Alarm_Check       rtc.o
    0x08001300   0x08001300   0x0000000e   Code   RO         3808    i.Alarm_Init        rtc.o
    0x0800130e   0x0800130e   0x00000004   Code   RO         3419    i.BusFault_Handler  stm32f10x_it.o
    0x08001312   0x08001312   0x00000002   PAD
    0x08001314   0x08001314   0x00000040   Code   RO         3588    i.Clock_ChangePage  clock_display.o
    0x08001354   0x08001354   0x0000005c   Code   RO         3589    i.Clock_Display     clock_display.o
    0x080013b0   0x080013b0   0x000007c4   Code   RO         3590    i.Clock_DrawAlarmEdit  clock_display.o
    0x08001b74   0x08001b74   0x000002cc   Code   RO         3591    i.Clock_DrawAlarmList  clock_display.o
    0x08001e40   0x08001e40   0x00000254   Code   RO         3594    i.Clock_DrawDigitalTime  clock_display.o
    0x08002094   0x08002094   0x000006b4   Code   RO         3596    i.Clock_DrawTimeSet  clock_display.o
    0x08002748   0x08002748   0x00000024   Code   RO         3597    i.Clock_Init        clock_display.o
    0x0800276c   0x0800276c   0x00000854   Code   RO         3598    i.Clock_ProcessKey  clock_display.o
    0x08002fc0   0x08002fc0   0x000001ec   Code   RO         3599    i.Clock_RefreshTimeDigit  clock_display.o
    0x080031ac   0x080031ac   0x00000002   Code   RO         3420    i.DebugMon_Handler  stm32f10x_it.o
    0x080031ae   0x080031ae   0x00000116   Code   RO         1276    i.GPIO_Init         stm32f10x_gpio.o
    0x080032c4   0x080032c4   0x00000090   Code   RO         1278    i.GPIO_PinRemapConfig  stm32f10x_gpio.o
    0x08003354   0x08003354   0x00000012   Code   RO         1280    i.GPIO_ReadInputDataBit  stm32f10x_gpio.o
    0x08003366   0x08003366   0x00000004   Code   RO         1284    i.GPIO_SetBits      stm32f10x_gpio.o
    0x0800336a   0x0800336a   0x0000007e   Code   RO         3600    i.GetDayOfWeek      clock_display.o
    0x080033e8   0x080033e8   0x00000004   Code   RO         3421    i.HardFault_Handler  stm32f10x_it.o
    0x080033ec   0x080033ec   0x00000032   Code   RO         3811    i.IsLeapYear        rtc.o
    0x0800341e   0x0800341e   0x00000002   PAD
    0x08003420   0x08003420   0x00000058   Code   RO         3915    i.KEY_Init          key.o
    0x08003478   0x08003478   0x000000a4   Code   RO         3916    i.KEY_Scan          key.o
    0x0800351c   0x0800351c   0x00000088   Code   RO         3197    i.LCD_Clear         lcd.o
    0x080035a4   0x080035a4   0x000001c0   Code   RO         3201    i.LCD_Display_Dir   lcd.o
    0x08003764   0x08003764   0x000000b0   Code   RO         3202    i.LCD_DrawLine      lcd.o
    0x08003814   0x08003814   0x00000040   Code   RO         3203    i.LCD_DrawPoint     lcd.o
    0x08003854   0x08003854   0x0000003c   Code   RO         3204    i.LCD_DrawRectangle  lcd.o
    0x08003890   0x08003890   0x00000364   Code   RO         3206    i.LCD_Fast_DrawPoint  lcd.o
    0x08003bf4   0x08003bf4   0x000000d4   Code   RO         3207    i.LCD_Fill          lcd.o
    0x08003cc8   0x08003cc8   0x00003bbc   Code   RO         3208    i.LCD_Init          lcd.o
    0x08007884   0x08007884   0x00000070   Code   RO         3210    i.LCD_RD_DATA       lcd.o
    0x080078f4   0x080078f4   0x00000010   Code   RO         3212    i.LCD_ReadReg       lcd.o
    0x08007904   0x08007904   0x000000d4   Code   RO         3213    i.LCD_SSD_BackLightSet  lcd.o
    0x080079d8   0x080079d8   0x00000458   Code   RO         3214    i.LCD_Scan_Dir      lcd.o
    0x08007e30   0x08007e30   0x000003bc   Code   RO         3215    i.LCD_SetCursor     lcd.o
    0x080081ec   0x080081ec   0x00000128   Code   RO         3217    i.LCD_ShowChar      lcd.o
    0x08008314   0x08008314   0x00000066   Code   RO         3219    i.LCD_ShowString    lcd.o
    0x0800837a   0x0800837a   0x00000002   PAD
    0x0800837c   0x0800837c   0x00000024   Code   RO         3221    i.LCD_WR_DATAX      lcd.o
    0x080083a0   0x080083a0   0x00000024   Code   RO         3222    i.LCD_WR_REG        lcd.o
    0x080083c4   0x080083c4   0x00000010   Code   RO         3224    i.LCD_WriteRAM_Prepare  lcd.o
    0x080083d4   0x080083d4   0x00000030   Code   RO         3225    i.LCD_WriteReg      lcd.o
    0x08008404   0x08008404   0x0000000c   Code   RO         3739    i.LED1_Off          led.o
    0x08008410   0x08008410   0x0000000c   Code   RO         3740    i.LED1_On           led.o
    0x0800841c   0x0800841c   0x00000018   Code   RO         3741    i.LED1_Toggle       led.o
    0x08008434   0x08008434   0x0000000c   Code   RO         3742    i.LED2_Off          led.o
    0x08008440   0x08008440   0x0000000c   Code   RO         3743    i.LED2_On           led.o
    0x0800844c   0x0800844c   0x00000018   Code   RO         3744    i.LED2_Toggle       led.o
    0x08008464   0x08008464   0x0000005c   Code   RO         3745    i.LED_Init          led.o
    0x080084c0   0x080084c0   0x00000048   Code   RO         3746    i.LED_StartBlink    led.o
    0x08008508   0x08008508   0x00000018   Code   RO         3747    i.LED_StopBlink     led.o
    0x08008520   0x08008520   0x00000004   Code   RO         3422    i.MemManage_Handler  stm32f10x_it.o
    0x08008524   0x08008524   0x00000002   Code   RO         3423    i.NMI_Handler       stm32f10x_it.o
    0x08008526   0x08008526   0x00000002   PAD
    0x08008528   0x08008528   0x00000070   Code   RO            1    i.NVIC_Init         misc.o
    0x08008598   0x08008598   0x00000002   Code   RO         3424    i.PendSV_Handler    stm32f10x_it.o
    0x0800859a   0x0800859a   0x00000002   PAD
    0x0800859c   0x0800859c   0x00000020   Code   RO         1704    i.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x080085bc   0x080085bc   0x00000020   Code   RO         1706    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x080085dc   0x080085dc   0x000000d4   Code   RO         1714    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x080086b0   0x080086b0   0x0000002c   Code   RO         3813    i.RTC_Init          rtc.o
    0x080086dc   0x080086dc   0x00000014   Code   RO         3814    i.RTC_SetTime       rtc.o
    0x080086f0   0x080086f0   0x00000002   Code   RO         3425    i.SVC_Handler       stm32f10x_it.o
    0x080086f2   0x080086f2   0x00000002   PAD
    0x080086f4   0x080086f4   0x00000028   Code   RO         3602    i.SelectCallback_AddAlarm  clock_display.o
    0x0800871c   0x0800871c   0x00000024   Code   RO         3603    i.SelectCallback_CancelEdit  clock_display.o
    0x08008740   0x08008740   0x00000028   Code   RO         3604    i.SelectCallback_CancelTimeSet  clock_display.o
    0x08008768   0x08008768   0x00000020   Code   RO         3606    i.SelectCallback_EditAlarm  clock_display.o
    0x08008788   0x08008788   0x0000000a   Code   RO         3608    i.SelectCallback_GotoClock  clock_display.o
    0x08008792   0x08008792   0x00000002   PAD
    0x08008794   0x08008794   0x00000144   Code   RO         3609    i.SelectCallback_SaveAlarm  clock_display.o
    0x080088d8   0x080088d8   0x00000068   Code   RO         3610    i.SelectCallback_SaveTime  clock_display.o
    0x08008940   0x08008940   0x00000014   Code   RO         3611    i.SelectCallback_SetTime  clock_display.o
    0x08008954   0x08008954   0x00000008   Code   RO         3143    i.SetSysClock       system_stm32f10x.o
    0x0800895c   0x0800895c   0x000000e0   Code   RO         3144    i.SetSysClockTo72   system_stm32f10x.o
    0x08008a3c   0x08008a3c   0x00000060   Code   RO         3146    i.SystemInit        system_stm32f10x.o
    0x08008a9c   0x08008a9c   0x00000190   Code   RO         3426    i.TIM2_IRQHandler   stm32f10x_it.o
    0x08008c2c   0x08008c2c   0x0000005a   Code   RO         3879    i.TIM2_Init         timer.o
    0x08008c86   0x08008c86   0x00000002   PAD
    0x08008c88   0x08008c88   0x00000088   Code   RO         3427    i.TIM3_IRQHandler   stm32f10x_it.o
    0x08008d10   0x08008d10   0x00000060   Code   RO         3880    i.TIM3_Init         timer.o
    0x08008d70   0x08008d70   0x00000038   Code   RO         3881    i.TIM4_Init         timer.o
    0x08008da8   0x08008da8   0x00000006   Code   RO         2345    i.TIM_ClearITPendingBit  stm32f10x_tim.o
    0x08008dae   0x08008dae   0x00000018   Code   RO         2350    i.TIM_Cmd           stm32f10x_tim.o
    0x08008dc6   0x08008dc6   0x00000006   Code   RO         2369    i.TIM_GetCounter    stm32f10x_tim.o
    0x08008dcc   0x08008dcc   0x00000022   Code   RO         2371    i.TIM_GetITStatus   stm32f10x_tim.o
    0x08008dee   0x08008dee   0x00000012   Code   RO         2375    i.TIM_ITConfig      stm32f10x_tim.o
    0x08008e00   0x08008e00   0x00000004   Code   RO         2415    i.TIM_SetCounter    stm32f10x_tim.o
    0x08008e04   0x08008e04   0x000000a4   Code   RO         2421    i.TIM_TimeBaseInit  stm32f10x_tim.o
    0x08008ea8   0x08008ea8   0x00000270   Code   RO         3934    i.UART_Parse_Alarm  uart_protocol.o
    0x08009118   0x08009118   0x00000088   Code   RO         3935    i.UART_Parse_Command  uart_protocol.o
    0x080091a0   0x080091a0   0x000001d0   Code   RO         3936    i.UART_Parse_Time   uart_protocol.o
    0x08009370   0x08009370   0x000000b4   Code   RO         3937    i.UART_Protocol_Init  uart_protocol.o
    0x08009424   0x08009424   0x00000128   Code   RO         3938    i.UART_Protocol_Process  uart_protocol.o
    0x0800954c   0x0800954c   0x000001a4   Code   RO         3939    i.UART_Send_Alarm   uart_protocol.o
    0x080096f0   0x080096f0   0x000006c4   Code   RO         3940    i.UART_Send_Help    uart_protocol.o
    0x08009db4   0x08009db4   0x0000005c   Code   RO         3941    i.UART_Send_Time    uart_protocol.o
    0x08009e10   0x08009e10   0x000000b4   Code   RO         3541    i.USART1_IRQHandler  usart.o
    0x08009ec4   0x08009ec4   0x00000018   Code   RO         2889    i.USART_Cmd         stm32f10x_usart.o
    0x08009edc   0x08009edc   0x0000001a   Code   RO         2892    i.USART_GetFlagStatus  stm32f10x_usart.o
    0x08009ef6   0x08009ef6   0x00000054   Code   RO         2893    i.USART_GetITStatus  stm32f10x_usart.o
    0x08009f4a   0x08009f4a   0x0000004a   Code   RO         2895    i.USART_ITConfig    stm32f10x_usart.o
    0x08009f94   0x08009f94   0x000000d8   Code   RO         2896    i.USART_Init        stm32f10x_usart.o
    0x0800a06c   0x0800a06c   0x0000000a   Code   RO         2903    i.USART_ReceiveData  stm32f10x_usart.o
    0x0800a076   0x0800a076   0x00000008   Code   RO         2906    i.USART_SendData    stm32f10x_usart.o
    0x0800a07e   0x0800a07e   0x00000036   Code   RO         3542    i.USART_SendString  usart.o
    0x0800a0b4   0x0800a0b4   0x00000004   Code   RO         3428    i.UsageFault_Handler  stm32f10x_it.o
    0x0800a0b8   0x0800a0b8   0x0000000e   Code   RO         4039    i._is_digit         c_w.l(__printf_wp.o)
    0x0800a0c6   0x0800a0c6   0x00000004   Code   RO         3543    i._sys_exit         usart.o
    0x0800a0ca   0x0800a0ca   0x0000001a   Code   RO         3882    i.delay_ms          timer.o
    0x0800a0e4   0x0800a0e4   0x00000020   Code   RO         3883    i.delay_us          timer.o
    0x0800a104   0x0800a104   0x00000024   Code   RO         3544    i.fputc             usart.o
    0x0800a128   0x0800a128   0x000001dc   Code   RO         3500    i.main              main.o
    0x0800a304   0x0800a304   0x000000a0   Code   RO         3545    i.uart1_init        usart.o
    0x0800a3a4   0x0800a3a4   0x0000002c   Code   RO         4245    locale$$code        c_w.l(lc_ctype_c.o)
    0x0800a3d0   0x0800a3d0   0x0000005a   Code   RO         4101    x$fpl$dfixu         fz_ws.l(dfixu.o)
    0x0800a42a   0x0800a42a   0x00000026   Code   RO         4105    x$fpl$dfltu         fz_ws.l(dflt_clz.o)
    0x0800a450   0x0800a450   0x00000154   Code   RO         4111    x$fpl$dmul          fz_ws.l(dmul.o)
    0x0800a5a4   0x0800a5a4   0x0000009c   Code   RO         4162    x$fpl$dnaninf       fz_ws.l(dnaninf.o)
    0x0800a640   0x0800a640   0x0000000c   Code   RO         4164    x$fpl$dretinf       fz_ws.l(dretinf.o)
    0x0800a64c   0x0800a64c   0x00000000   Code   RO         4170    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x0800a64c   0x0800a64c   0x000017c0   Data   RO         3228    .constdata          lcd.o
    0x0800be0c   0x0800be0c   0x0000000c   Data   RO         3816    .constdata          rtc.o
    0x0800be18   0x0800be18   0x00000028   Data   RO         4012    .constdata          c_w.l(_printf_hex_int.o)
    0x0800be40   0x0800be40   0x00000011   Data   RO         4042    .constdata          c_w.l(__printf_flags_wp.o)
    0x0800be51   0x0800be51   0x00000003   PAD
    0x0800be54   0x0800be54   0x0000005d   Data   RO         3613    .conststring        clock_display.o
    0x0800beb1   0x0800beb1   0x00000003   PAD
    0x0800beb4   0x0800beb4   0x00000020   Data   RO         4383    Region$$Table       anon$$obj.o
    0x0800bed4   0x0800bed4   0x00000110   Data   RO         4244    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800bfe4, Size: 0x00000910, Max: 0x0000c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x0800bfe4   0x00000014   Data   RW         1734    .data               stm32f10x_rcc.o
    0x20000014   0x0800bff8   0x00000004   Data   RW         3229    .data               lcd.o
    0x20000018   0x0800bffc   0x00000001   Data   RW         3429    .data               stm32f10x_it.o
    0x20000019   0x0800bffd   0x00000003   PAD
    0x2000001c   0x0800c000   0x00000008   Data   RW         3547    .data               usart.o
    0x20000024   0x0800c008   0x00000080   Data   RW         3614    .data               clock_display.o
    0x200000a4   0x0800c088   0x00000004   Data   RW         3817    .data               rtc.o
    0x200000a8   0x0800c08c   0x00000001   Data   RW         3917    .data               key.o
    0x200000a9   0x0800c08d   0x00000003   PAD
    0x200000ac        -       0x0000000e   Zero   RW         3227    .bss                lcd.o
    0x200000ba        -       0x000000c8   Zero   RW         3546    .bss                usart.o
    0x20000182        -       0x00000024   Zero   RW         3612    .bss                clock_display.o
    0x200001a6        -       0x00000105   Zero   RW         3815    .bss                rtc.o
    0x200002ab   0x0800c08d   0x00000001   PAD
    0x200002ac        -       0x00000060   Zero   RW         4200    .bss                c_w.l(libspace.o)
    0x2000030c   0x0800c08d   0x00000004   PAD
    0x20000310        -       0x00000200   Zero   RW         3137    HEAP                startup_stm32f10x_md.o
    0x20000510        -       0x00000400   Zero   RW         3136    STACK               startup_stm32f10x_md.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      8564       1740         93        128         36      12687   clock_display.o
         0          0          0          0          0         32   core_cm3.o
       252         22          0          1          0       1162   key.o
     20198        264       6080          4         14      24470   lcd.o
       284         72          0          0          0       3382   led.o
       476        204          0          0          0       1175   main.o
       112         12          0          0          0     253356   misc.o
       402         72         12          4        261       4456   rtc.o
        64         26        236          0       1536        760   startup_stm32f10x_md.o
         0          0          0          0          0       1624   stm32f10x_adc.o
       444          6          0          0          0      11889   stm32f10x_gpio.o
       560        136          0          1          0       5007   stm32f10x_it.o
       276         32          0         20          0      12758   stm32f10x_rcc.o
       256         42          0          0          0      24336   stm32f10x_tim.o
       442          6          0          0          0      11573   stm32f10x_usart.o
       328         28          0          0          0       1693   system_stm32f10x.o
       300         16          0          0          0       2514   timer.o
      3944       2504          0          0          0       5487   uart_protocol.o
       434         24          0          8        200       4368   usart.o

    ----------------------------------------------------------------------
     37352       <USER>       <GROUP>        172       2048     382729   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        16          0          3          6          1          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        60          8          0          0          0         84   __0sscanf.o
         8          0          0          0          0         68   __main.o
       312          4         17          0          0         92   __printf_flags_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        28          0          0          0          0         68   _chval.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
        88          4         40          0          0         88   _printf_hex_int.o
       178          0          0          0          0         88   _printf_intcommon.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
         6          0          0          0          0          0   _printf_x.o
       884          4          0          0          0        100   _scanf.o
       332          0          0          0          0         96   _scanf_int.o
       224          0          0          0          0         96   _scanf_str.o
        64          0          0          0          0         84   _sgetc.o
        10          0          0          0          0         68   _sputc.o
       158          0          0          0          0         92   _strtoul.o
        26          0          0          0          0         80   atoi.o
        18          0          0          0          0         80   exit.o
         8          0          0          0          0         68   ferror.o
         6          0          0          0          0        152   heapauxi.o
        18          0          0          0          0         76   isspace.o
        44         10        272          0          0         76   lc_ctype_c.o
         2          0          0          0          0          0   libinit.o
        20          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        24          4          0          0          0         84   noretval__2printf.o
        40          6          0          0          0         84   noretval__2sprintf.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        44          8          0          0          0         84   scanf_char.o
        24          0          0          0          0         68   strcat.o
        20          0          0          0          0         68   strchr.o
       128          0          0          0          0         68   strcmpv7m.o
        72          0          0          0          0         80   strcpy.o
        62          0          0          0          0         76   strlen.o
       150          0          0          0          0         80   strncmp.o
        86          0          0          0          0         76   strncpy.o
       112          0          0          0          0         88   strtol.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
        90          4          0          0          0         92   dfixu.o
        38          0          0          0          0         68   dflt_clz.o
       340         12          0          0          0        104   dmul.o
       156          4          0          0          0         92   dnaninf.o
        12          0          0          0          0         68   dretinf.o
         0          0          0          0          0          0   usenofp.o

    ----------------------------------------------------------------------
      4984        <USER>        <GROUP>          0        100       4260   Library Totals
        12          0          3          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      4336         98        329          0         96       3836   c_w.l
       636         20          0          0          0        424   fz_ws.l

    ----------------------------------------------------------------------
      4984        <USER>        <GROUP>          0        100       4260   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     42336       5324       6788        172       2148     379133   Grand Totals
     42336       5324       6788        172       2148     379133   ELF Image Totals
     42336       5324       6788        172          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                49124 (  47.97kB)
    Total RW  Size (RW Data + ZI Data)              2320 (   2.27kB)
    Total ROM Size (Code + RO Data + RW Data)      49296 (  48.14kB)

==============================================================================

