#include "led.h"
#include "sys.h"

// LED引脚定义
#define LED1_PIN     GPIO_Pin_8
#define LED1_PORT    GPIOA
#define LED1_CLK     RCC_APB2Periph_GPIOA
#define LED2_PIN     GPIO_Pin_2
#define LED2_PORT    GPIOD
#define LED2_CLK     RCC_APB2Periph_GPIOD

// LED控制宏定义
#define LED1 PAout(8) // PA8
#define LED2 PDout(2) // PD2

// 初始化LED
void LED_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;

    // 使能LED1端口时钟
    RCC_APB2PeriphClockCmd(LED1_CLK | RCC_APB2Periph_AFIO, ENABLE);

    // 配置LED1引脚
    GPIO_InitStructure.GPIO_Pin = LED1_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP; // 推挽输出
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(LED1_PORT, &GPIO_InitStructure);

    // 使能LED2端口时钟
    RCC_APB2PeriphClockCmd(LED2_CLK | RCC_APB2Periph_AFIO, ENABLE);

    // 配置LED2引脚
    GPIO_InitStructure.GPIO_Pin = LED2_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP; // 推挽输出
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(LED2_PORT, &GPIO_InitStructure);

    // 默认关闭LED
    LED1_Off();
    LED2_Off();
}

// 打开LED1
void LED1_On(void)
{
    LED1 = 0; // LED低电平点亮
}

// 关闭LED1
void LED1_Off(void)
{
    LED1 = 1; // LED高电平熄灭
}

// 切换LED1状态
void LED1_Toggle(void)
{
    LED1 = !LED1;
}

// 打开LED2
void LED2_On(void)
{
    LED2 = 0; // LED低电平点亮
}

// 关闭LED2
void LED2_Off(void)
{
    LED2 = 1; // LED高电平熄灭
}

// 切换LED2状态
void LED2_Toggle(void)
{
    LED2 = !LED2;
}

// 同时切换所有LED状态
void LED_Toggle(void)
{
    LED1_Toggle();
    LED2_Toggle();
}

// 开始LED闪烁
void LED_StartBlink(void)
{
    // 打印调试信息
    printf("开始LED闪烁...\r\n");

    // 先停止定时器确保从头开始
    TIM_Cmd(TIM3, DISABLE);

    // 设置计数器
    TIM_SetCounter(TIM3, 0);

    // 确保LED处于亮状态
    LED1_On();
    LED2_On();

    // 启动TIM3定时器控制LED闪烁
    TIM_Cmd(TIM3, ENABLE);
}

// 停止LED闪烁
void LED_StopBlink(void)
{
    // 停止TIM3定时器
    TIM_Cmd(TIM3, DISABLE);

    // 关闭LED
    LED1_Off();
    LED2_Off();
}