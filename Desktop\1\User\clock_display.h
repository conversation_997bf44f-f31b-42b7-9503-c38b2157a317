#ifndef __CLOCK_DISPLAY_H
#define __CLOCK_DISPLAY_H

#include "stm32f10x.h"
#include "lcd.h"
#include "rtc.h"

// 页面定义
#define PAGE_CLOCK       0   // 时钟页面
#define PAGE_ALARM_LIST  1   // 闹钟列表页面
#define PAGE_ALARM_EDIT  2   // 闹钟编辑页面
#define PAGE_TIME_SET    3   // 时间设置页面

// 模拟时钟参数
#define CLOCK_CENTER_X   120       // 时钟中心X坐标
#define CLOCK_CENTER_Y   120       // 时钟中心Y坐标
#define CLOCK_RADIUS     60        // 时钟半径
#define HOUR_HAND_LEN    30        // 时针长度
#define MINUTE_HAND_LEN  45        // 分针长度
#define SECOND_HAND_LEN  50        // 秒针长度

// 函数声明
void Clock_Init(void);
void Clock_Display(DateTime* time);
void Clock_DrawDigitalTime(DateTime* time);
void Clock_DrawDate(DateTime* time);
void Clock_DrawDial(void);
void Clock_DrawHands(DateTime* time);
void Clock_DrawAlarmList(void);
void Clock_DrawAlarmEdit(uint8_t index);
void Clock_DrawTimeSet(DateTime* time);
void Clock_ChangePage(uint8_t page);
void Clock_ProcessKey(uint8_t key);
uint8_t GetDayOfWeek(uint16_t year, uint8_t month, uint8_t day);

#endif /* __CLOCK_DISPLAY_H */ 