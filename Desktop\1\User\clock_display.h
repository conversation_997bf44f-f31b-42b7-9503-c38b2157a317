#ifndef __CLOCK_DISPLAY_H
#define __CLOCK_DISPLAY_H

#include "stm32f10x.h"
#include "lcd.h"
#include "rtc.h"
#include <math.h>

// 页面定义
#define PAGE_CLOCK       0   // 时钟页面
#define PAGE_ALARM_LIST  1   // 闹钟列表页面
#define PAGE_ALARM_EDIT  2   // 闹钟编辑页面
#define PAGE_TIME_SET    3   // 时间设置页面

// 模拟时钟参数 - 重新调整避免冲突
#define CLOCK_CENTER_X   120       // 时钟中心X坐标 (左移避免右边界)
#define CLOCK_CENTER_Y   150       // 时钟中心Y坐标 (下移避免与日期冲突)
#define CLOCK_RADIUS     60        // 时钟半径 (适中大小确保完全显示)
#define HOUR_HAND_LEN    35        // 时针长度 (短粗)
#define MINUTE_HAND_LEN  45        // 分针长度 (中等)
#define SECOND_HAND_LEN  55        // 秒针长度 (最长)

// 表盘装饰参数
#define HOUR_MARK_LEN    8         // 小时刻度长度
#define MINUTE_MARK_LEN  4         // 分钟刻度长度
#define CENTER_DOT_RADIUS 3        // 中心圆点半径

// 数字显示位置 - 重新布局
#define DATE_DISPLAY_X   120       // 日期显示X坐标 (居中)
#define DATE_DISPLAY_Y   20        // 日期显示Y坐标 (表盘上方留足空间)
#define DIGITAL_TIME_X   120       // 数字时间X坐标 (居中)
#define DIGITAL_TIME_Y   230       // 数字时间Y坐标 (表盘下方)

// 屏幕按钮参数
#define BUTTON_WIDTH     60        // 按钮宽度
#define BUTTON_HEIGHT    30        // 按钮高度
#define BUTTON_Y         270       // 按钮Y坐标 (在数字时间下方)

#define BUTTON1_X        30        // 按钮1 X坐标 (WK_UP功能)
#define BUTTON2_X        100       // 按钮2 X坐标 (KEY0功能)
#define BUTTON3_X        170       // 按钮3 X坐标 (KEY1功能)

// 函数声明
void Clock_Init(void);
void Clock_Display(DateTime* time);
void Clock_DrawDigitalTime(DateTime* time);
void Clock_DrawDate(DateTime* time);
void Clock_DrawDial(void);
void Clock_DrawHands(DateTime* time);
void Clock_DrawAlarmList(void);
void Clock_DrawAlarmEdit(uint8_t index);
void Clock_DrawTimeSet(DateTime* time);
void Clock_ChangePage(uint8_t page);
void Clock_ProcessKey(uint8_t key);
uint8_t GetDayOfWeek(uint16_t year, uint8_t month, uint8_t day);

// 新增的圆盘时钟绘制函数
void Clock_DrawAnalogClock(DateTime* time);
void Clock_DrawClockFace(void);
void Clock_DrawHourMarks(void);
void Clock_DrawMinuteMarks(void);
void Clock_DrawNumbers(void);
void Clock_DrawHand(int16_t angle, uint8_t length, uint16_t color, uint8_t thickness);
void Clock_ClearHands(DateTime* last_time);
void Clock_DrawCenterDot(void);
void Clock_DrawDateInfo(DateTime* time);
int16_t Clock_CalculateAngle(uint8_t value, uint8_t max_value);

// 辅助函数
void DrawThickLine(int16_t x1, int16_t y1, int16_t x2, int16_t y2, uint16_t color, uint8_t thickness);
const char* GetWeekdayName(uint8_t weekday);
const char* GetMonthName(uint8_t month);

// 屏幕按钮相关函数
void Clock_DrawScreenButtons(void);
void Clock_DrawButton(uint16_t x, uint16_t y, uint16_t width, uint16_t height,
                     const char* text, uint16_t bg_color, uint16_t text_color);
uint8_t Clock_CheckButtonPress(uint16_t touch_x, uint16_t touch_y);
void Clock_ProcessButtonPress(uint8_t button_id);

// 新增的功能函数
void Clock_ShowSystemStatus(void);
void Clock_AddNewAlarm(void);
void Clock_ToggleAlarmEnabled(void);
void Clock_ResetToSystemTime(void);

#endif /* __CLOCK_DISPLAY_H */