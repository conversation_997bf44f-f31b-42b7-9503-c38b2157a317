#ifndef __CLOCK_DISPLAY_H
#define __CLOCK_DISPLAY_H

#include "stm32f10x.h"
#include "lcd.h"
#include "rtc.h"
#include <math.h>

// 页面定义
#define PAGE_CLOCK       0   // 时钟页面
#define PAGE_ALARM_LIST  1   // 闹钟列表页面
#define PAGE_ALARM_EDIT  2   // 闹钟编辑页面
#define PAGE_TIME_SET    3   // 时间设置页面

// 模拟时钟参数 - 优化为更大更美观的圆盘
#define CLOCK_CENTER_X   240       // 时钟中心X坐标 (屏幕中央)
#define CLOCK_CENTER_Y   200       // 时钟中心Y坐标 (留出上方空间显示日期)
#define CLOCK_RADIUS     80        // 时钟半径 (增大表盘)
#define HOUR_HAND_LEN    45        // 时针长度
#define MINUTE_HAND_LEN  65        // 分针长度
#define SECOND_HAND_LEN  70        // 秒针长度

// 表盘装饰参数
#define HOUR_MARK_LEN    12        // 小时刻度长度
#define MINUTE_MARK_LEN  6         // 分钟刻度长度
#define CENTER_DOT_RADIUS 3        // 中心圆点半径

// 数字显示位置
#define DATE_DISPLAY_X   200       // 日期显示X坐标
#define DATE_DISPLAY_Y   80        // 日期显示Y坐标
#define DIGITAL_TIME_X   200       // 数字时间X坐标
#define DIGITAL_TIME_Y   320       // 数字时间Y坐标

// 函数声明
void Clock_Init(void);
void Clock_Display(DateTime* time);
void Clock_DrawDigitalTime(DateTime* time);
void Clock_DrawDate(DateTime* time);
void Clock_DrawDial(void);
void Clock_DrawHands(DateTime* time);
void Clock_DrawAlarmList(void);
void Clock_DrawAlarmEdit(uint8_t index);
void Clock_DrawTimeSet(DateTime* time);
void Clock_ChangePage(uint8_t page);
void Clock_ProcessKey(uint8_t key);
uint8_t GetDayOfWeek(uint16_t year, uint8_t month, uint8_t day);

// 新增的圆盘时钟绘制函数
void Clock_DrawAnalogClock(DateTime* time);
void Clock_DrawClockFace(void);
void Clock_DrawHourMarks(void);
void Clock_DrawMinuteMarks(void);
void Clock_DrawNumbers(void);
void Clock_DrawHand(int16_t angle, uint8_t length, uint16_t color, uint8_t thickness);
void Clock_ClearHands(DateTime* last_time);
void Clock_DrawCenterDot(void);
void Clock_DrawDateInfo(DateTime* time);
int16_t Clock_CalculateAngle(uint8_t value, uint8_t max_value);

// 辅助函数
void DrawThickLine(int16_t x1, int16_t y1, int16_t x2, int16_t y2, uint16_t color, uint8_t thickness);
const char* GetWeekdayName(uint8_t weekday);
const char* GetMonthName(uint8_t month);

#endif /* __CLOCK_DISPLAY_H */