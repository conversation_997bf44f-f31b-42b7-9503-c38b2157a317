#include "key.h"
#include "delay.h"

// 初始化按键
void KEY_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    
    // 使能按键端口时钟
    RCC_APB2PeriphClockCmd(KEY1_CLK | KEY2_CLK | KEY3_CLK, ENABLE);
    
    // 配置KEY1引脚 (PA15) - 页面切换
    GPIO_InitStructure.GPIO_Pin = KEY1_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU; // 上拉输入
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(KEY1_PORT, &GPIO_InitStructure);
    
    // 配置KEY2引脚 (PC5) - 确定
    GPIO_InitStructure.GPIO_Pin = KEY2_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU; // 上拉输入
    GPIO_Init(KEY2_PORT, &GPIO_InitStructure);
    
    // 配置KEY3引脚 (PA0) - 选择
    GPIO_InitStructure.GPIO_Pin = KEY3_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPD; // 下拉输入
    GPIO_Init(KEY3_PORT, &GPIO_InitStructure);
}

// 按键扫描函数
// mode: 0-不支持连续按; 1-支持连续按
uint8_t KEY_Scan(uint8_t mode)
{
    static uint8_t key_up = 1; // 按键松开标志
    
    // 读取当前按键状态
    uint8_t key1_val = GPIO_ReadInputDataBit(KEY1_PORT, KEY1_PIN); // PA15 上拉输入
    uint8_t key2_val = GPIO_ReadInputDataBit(KEY2_PORT, KEY2_PIN); // PC5 上拉输入
    uint8_t key3_val = GPIO_ReadInputDataBit(KEY3_PORT, KEY3_PIN); // PA0 下拉输入
    
    // 检查是否所有按键都处于释放状态
    // PA15和PC5是上拉输入，未按下时为高电平(1)
    // PA0是下拉输入，未按下时为低电平(0)
    if(key1_val == 1 && key2_val == 1 && key3_val == 0)
    {
        key_up = 1; // 所有按键都释放了
    }
    
    if(mode) key_up = 1; // 支持连续按
    
    if(key_up)
    {
        delay_ms(10); // 消抖
        
        // 再次读取按键状态
        key1_val = GPIO_ReadInputDataBit(KEY1_PORT, KEY1_PIN);
        key2_val = GPIO_ReadInputDataBit(KEY2_PORT, KEY2_PIN);
        key3_val = GPIO_ReadInputDataBit(KEY3_PORT, KEY3_PIN);
        
        // PA15(KEY1)按下检测 - 上拉输入，按下为低电平(0)
        if(key1_val == 0)
        {
            key_up = 0;
            return KEY1_PRESSED;
        }
        
        // PC5(KEY2)按下检测 - 上拉输入，按下为低电平(0)
        if(key2_val == 0)
        {
            key_up = 0;
            return KEY2_PRESSED;
        }
        
        // PA0(KEY3)按下检测 - 下拉输入，按下为高电平(1)
        if(key3_val == 1)
        {
            key_up = 0;
            return KEY3_PRESSED;
        }
    }
    
    return KEY_NONE;
} 